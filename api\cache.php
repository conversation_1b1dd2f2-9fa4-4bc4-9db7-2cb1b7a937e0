<?php
// Cache Management API - PHP 8.4.7
// Handles cache clearing, version management, and cache status monitoring

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Security check - require special key for cache operations
$CACHE_SECRET_KEY = 'squid456_cache_key_' . date('Y-m-d'); // Changes daily for security

// Include the cache manager
require_once __DIR__ . '/../index.php';

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['cache_action'] ?? '';

// Verify authorization for destructive operations
function verifyAuth() {
    global $CACHE_SECRET_KEY;
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $providedKey = $_GET['key'] ?? '';
    
    return ($authHeader === 'Bearer ' . $CACHE_SECRET_KEY) || ($providedKey === $CACHE_SECRET_KEY);
}

switch ($method) {
    case 'GET':
        handleGetRequest($action);
        break;
    case 'POST':
        handlePostRequest($action);
        break;
    case 'DELETE':
        handleDeleteRequest($action);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
}

function handleGetRequest($action) {
    global $cacheManager;
    
    switch ($action) {
        case 'status':
            getCacheStatus();
            break;
        case 'version':
            getCacheVersion();
            break;
        case 'logs':
            if (!verifyAuth()) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }
            getCacheLogs();
            break;
        default:
            getCacheInfo();
    }
}

function handlePostRequest($action) {
    global $cacheManager;
    
    if (!verifyAuth()) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized - Invalid cache key']);
        return;
    }
    
    switch ($action) {
        case 'clear':
            clearCache();
            break;
        case 'restore':
            restoreCache();
            break;
        case 'backup':
            backupCache();
            break;
        case 'force_refresh':
            forceRefresh();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handleDeleteRequest($action) {
    if (!verifyAuth()) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }
    
    switch ($action) {
        case 'all':
            clearAllCache();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid delete action']);
    }
}

function getCacheInfo() {
    global $cacheManager;
    
    echo json_encode([
        'success' => true,
        'cache_info' => [
            'current_version' => $cacheManager->getCurrentVersion(),
            'cache_bust_param' => $cacheManager->getCacheBustParam(),
            'timestamp' => date('Y-m-d H:i:s'),
            'server_time' => time(),
            'php_version' => phpversion(),
            'cache_headers_enabled' => function_exists('apache_get_modules') && in_array('mod_expires', apache_get_modules()),
            'service_worker_version' => 'squidlink-v1.0.0'
        ]
    ]);
}

function getCacheStatus() {
    global $cacheManager;
    
    $cacheDir = __DIR__ . '/../cache';
    $hasBackup = file_exists($cacheDir . '/version_backup.txt');
    
    echo json_encode([
        'success' => true,
        'status' => [
            'current_version' => $cacheManager->getCurrentVersion(),
            'has_backup' => $hasBackup,
            'backup_version' => $hasBackup ? trim(file_get_contents($cacheDir . '/version_backup.txt')) : null,
            'cache_directory_writable' => is_writable($cacheDir),
            'last_cleared' => getLastCacheEvent('cache_cleared'),
            'last_restored' => getLastCacheEvent('cache_restored')
        ]
    ]);
}

function getCacheVersion() {
    global $cacheManager;
    
    echo json_encode([
        'success' => true,
        'version' => $cacheManager->getCurrentVersion(),
        'cache_bust' => $cacheManager->getCacheBustParam(),
        'timestamp' => time()
    ]);
}

function getCacheLogs() {
    $logDir = __DIR__ . '/../logs';
    $logFile = $logDir . '/cache_' . date('Y-m-d') . '.log';
    
    $logs = [];
    if (file_exists($logFile)) {
        $content = file_get_contents($logFile);
        $entries = explode("\n\n", trim($content));
        
        foreach ($entries as $entry) {
            if (!empty($entry)) {
                $decoded = json_decode($entry, true);
                if ($decoded) {
                    $logs[] = $decoded;
                }
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'logs' => array_reverse($logs), // Most recent first
        'total_entries' => count($logs)
    ]);
}

function clearCache() {
    global $cacheManager;
    
    // Backup current version before clearing
    $cacheManager->backupCurrentVersion();
    
    // Clear cache
    $result = $cacheManager->clearCache();
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Cache cleared successfully',
            'new_version' => $cacheManager->getCurrentVersion(),
            'cache_bust' => $cacheManager->getCacheBustParam(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to clear cache']);
    }
}

function restoreCache() {
    global $cacheManager;
    
    $result = $cacheManager->restoreCache();
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Cache restored successfully',
            'restored_version' => $cacheManager->getCurrentVersion(),
            'cache_bust' => $cacheManager->getCacheBustParam(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'No backup version available to restore']);
    }
}

function backupCache() {
    global $cacheManager;
    
    $cacheManager->backupCurrentVersion();
    
    echo json_encode([
        'success' => true,
        'message' => 'Cache version backed up successfully',
        'current_version' => $cacheManager->getCurrentVersion(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

function forceRefresh() {
    global $cacheManager;
    
    // Generate new version and clear everything
    $cacheManager->backupCurrentVersion();
    $cacheManager->generateNewVersion();
    
    // Clear PHP opcache if available
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Force refresh completed - all caches cleared',
        'new_version' => $cacheManager->getCurrentVersion(),
        'cache_bust' => $cacheManager->getCacheBustParam(),
        'opcache_cleared' => function_exists('opcache_reset'),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

function clearAllCache() {
    global $cacheManager;
    
    // Clear all cache files
    $cacheDir = __DIR__ . '/../cache';
    $files = glob($cacheDir . '/*');
    
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    
    // Regenerate version
    $cacheManager->generateNewVersion();
    
    echo json_encode([
        'success' => true,
        'message' => 'All cache files deleted and version regenerated',
        'new_version' => $cacheManager->getCurrentVersion(),
        'files_deleted' => count($files),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

function getLastCacheEvent($eventType) {
    $logDir = __DIR__ . '/../logs';
    $logFile = $logDir . '/cache_' . date('Y-m-d') . '.log';
    
    if (!file_exists($logFile)) {
        return null;
    }
    
    $content = file_get_contents($logFile);
    $entries = explode("\n\n", trim($content));
    
    // Search from most recent to oldest
    for ($i = count($entries) - 1; $i >= 0; $i--) {
        $entry = json_decode($entries[$i], true);
        if ($entry && $entry['event'] === $eventType) {
            return $entry['timestamp'];
        }
    }
    
    return null;
}
?>
