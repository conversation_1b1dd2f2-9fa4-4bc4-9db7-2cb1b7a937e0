# SquidLink PHP - Player 456 Portfolio

🦑 **Squid Game themed portfolio website converted from Node.js/React to PHP 8.4.7**

Perfect for shared hosting platforms like **Hostinger** - no VPS required!

## ✨ Features

- **🎮 Squid Game Aesthetic** - Complete with guards, animations, and theme colors
- **📱 Mobile-First Design** - Fully responsive across all devices  
- **🔄 Name Cycling Animation** - FAHIM → HASAN → SANTO with glitch effects
- **📧 Contact Form** - PHP-powered with validation and email sending
- **🎯 Project Showcase** - Interactive 3D cards with loading animations
- **⚡ Skills Display** - Animated skill bars and mastery levels
- **📱 PWA Support** - Works offline with service worker
- **🔊 Audio Effects** - Hover sounds and interaction feedback
- **📱 NFC Support** - Tap-to-activate functionality
- **🎨 Custom Animations** - Glitch effects, particles, and transitions

## 🚀 Quick Deploy to Hostinger

### Method 1: Direct Upload
1. Download all files from `SquidLink-PHP/` folder
2. Upload to your Hostinger public_html directory
3. Done! Your site is live

### Method 2: File Manager
1. Login to Hostinger control panel
2. Open File Manager
3. Navigate to public_html
4. Upload the ZIP file and extract
5. Set permissions if needed (755 for folders, 644 for files)

## 📁 Project Structure

```
SquidLink-PHP/
├── index.php          # Main application file
├── .htaccess          # Apache configuration
├── manifest.json      # PWA manifest
├── sw.js             # Service worker
├── css/
│   └── style.css     # All styles and animations
├── js/
│   └── app.js        # Main JavaScript functionality
├── api/
│   ├── contact.php   # Contact form handler
│   └── nfc.php       # NFC event handler
├── assets/           # Images, sounds, icons
├── logs/            # Contact and NFC logs
└── README.md        # This file
```

## ⚙️ Configuration

### Email Setup
Edit `api/contact.php` line 54:
```php
$to = '<EMAIL>'; // Replace with your email
```

### Customization
- **Colors**: Edit CSS variables in `css/style.css`
- **Content**: Modify arrays in `index.php` (projects, skills, social links)
- **Animations**: Adjust timing in `css/style.css` and `js/app.js`

## 🎯 Key Differences from React Version

| Feature | React Version | PHP Version |
|---------|---------------|-------------|
| **Frontend** | React + Framer Motion | Vanilla JS + CSS animations |
| **Backend** | Node.js + Express | PHP 8.4.7 |
| **Routing** | Wouter | .htaccess rewrites |
| **Forms** | React Hook Form | Native PHP validation |
| **State** | React hooks | Session + localStorage |
| **Build** | Vite bundling | Direct file serving |
| **Hosting** | VPS required | Shared hosting ready |

## 🔧 Technical Features

### Animations
- **Glitch Text**: Pure CSS keyframes with multiple layers
- **3D Cards**: CSS transforms and perspective
- **Particle Background**: Particles.js integration
- **Loading Overlays**: Custom progress animations
- **Guard Figures**: CSS-only Squid Game characters

### PHP Features
- **Modern PHP 8.4.7** syntax and features
- **Input validation** with custom rules
- **Email sending** with HTML templates
- **Session management** for NFC tracking
- **Error logging** for debugging
- **Security headers** via .htaccess

### PWA Features
- **Service Worker** for offline functionality
- **Web App Manifest** for mobile installation
- **Background Sync** for form submissions
- **Push Notifications** ready (future use)
- **NFC Web API** integration

## 🎮 Interactive Elements

### Name Cycling
The hero text cycles through names every 3 seconds:
- FAHIM (3s) → HASAN (3s) → SANTO (3s) → repeat
- Enhanced glitch effects with color shifting
- Smooth transitions between names

### Project Cards
- **3D flip animation** on hover
- **Loading overlay** with Squid Game theme
- **External redirects** to live websites
- **Sound effects** on interaction

### Contact Form
- **Real-time validation** with error messages
- **Squid Game styling** with guard figures
- **Email sending** with HTML templates
- **Success/error feedback** with animations

### NFC Integration
- **Web NFC API** for card detection
- **Dramatic animations** on tap
- **Session tracking** and logging
- **PWA background processing**

## 🎨 Design Elements

### Squid Game Theme
- **Colors**: Pink (#ff0055), Green (#00ffaa), Black, White
- **Typography**: Bebas Neue (headers), Poppins (body)
- **Guards**: CSS-only figures with glowing red eyes
- **Animations**: Glitch effects, particle systems
- **UI Elements**: Game-inspired buttons and cards

### Mobile Responsive
- **Mobile-first** approach
- **Touch-friendly** interactions
- **Optimized animations** for mobile devices
- **PWA installation** support

## 🔊 Audio System

### Sound Effects
- **Hover sounds** for interactive elements
- **NFC detection** audio feedback
- **Form submission** success/error sounds
- **Fallback system** for unsupported browsers

### Implementation
- **Web Audio API** for modern browsers
- **HTML5 Audio** fallback
- **Preloaded assets** for smooth playback
- **User interaction** required for autoplay compliance

## 📱 NFC Functionality

### Features
- **Web NFC API** integration
- **Card tap detection** with visual feedback
- **Session tracking** and statistics
- **Background processing** via service worker
- **Fallback UI** for unsupported devices

### Usage
1. Program NFC tag with your website URL
2. Users tap their phone on the NFC card
3. Dramatic Squid Game animation plays
4. Website activates with full functionality

## 🚀 Performance

### Optimizations
- **Compressed assets** with gzip
- **Browser caching** via .htaccess
- **Lazy loading** for images
- **Minified code** for production
- **CDN integration** for external libraries

### Hosting Requirements
- **PHP 8.0+** (optimized for 8.4.7)
- **Apache** with mod_rewrite
- **50MB** disk space minimum
- **Standard shared hosting** compatible

## 🔒 Security

### Features
- **Input sanitization** for all forms
- **CSRF protection** via sessions
- **File access restrictions** via .htaccess
- **Security headers** for XSS protection
- **Error logging** without exposure

## 📞 Support

### Contact Information
- **Email**: Replace in `api/contact.php`
- **Social Media**: Update links in `index.php`
- **Portfolio**: Modify project URLs

### Customization Help
All code is well-commented and structured for easy modification. Key areas:
- **Content**: `index.php` arrays
- **Styling**: `css/style.css` variables
- **Functionality**: `js/app.js` class methods

---

**🦑 Ready to deploy your Squid Game portfolio? Upload and go live!**

*Converted from React/Node.js to PHP while maintaining 100% design and functionality parity.*
