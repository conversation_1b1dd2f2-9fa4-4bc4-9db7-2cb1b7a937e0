# NFC Contact System - <PERSON>ahi<PERSON>folio

## Overview
This system provides an interactive NFC-enabled contact experience for users visiting Fahim Santo's portfolio website. When users tap an NFC tag or click the NFC button, they get multiple contact options including auto-save to contacts and WhatsApp integration.

## Features

### 🧩 Auto Save to Contacts (.vcf File)
- **VCF File**: `fahim-contact.vcf` contains complete contact information
- **One-tap save**: Users can save contact directly to their phone's address book
- **Complete info**: Includes name, phone, email, social links, and professional details

### 💬 WhatsApp Integration
- **Direct link**: `https://wa.me/qr/3JDHGHLS2NAXN1`
- **Floating button**: Always visible WhatsApp contact button
- **Modal option**: WhatsApp contact available in NFC modal

### 📱 NFC Detection
- **Real NFC support**: Detects actual NFC tags when available
- **Manual trigger**: Pink NFC button for manual activation
- **Cross-platform**: Works on all devices with fallback options

## Files Modified/Created

### New Files
- `fahim-contact.vcf` - Contact information in vCard format
- `NFC_CONTACT_SYSTEM_README.md` - This documentation

### Modified Files
- `index.php` - Added NFC modal and floating WhatsApp button
- `js/app.js` - Added NFC modal functionality and tracking
- `css/style.css` - Added modal animations and button styles
- `api/nfc.php` - Enhanced NFC logging with contact options
- `api/contact.php` - Added contact action tracking

## How It Works

1. **NFC Detection**: User taps NFC tag or clicks NFC button
2. **Modal Display**: Contact options modal appears with animations
3. **User Choice**: Three options available:
   - Download VCF contact file
   - Open WhatsApp chat
   - View portfolio (scrolls to about section)
4. **Tracking**: All interactions are logged for analytics
5. **Notifications**: Success messages shown for user feedback

## Technical Implementation

### NFC Modal System
```javascript
// Auto-detects NFC support
if ('NDEFReader' in window) {
    // Real NFC functionality
} else {
    // Fallback to manual button
}
```

### Contact Tracking
- VCF downloads tracked
- WhatsApp clicks monitored
- Session-based analytics
- Daily log files generated

### Responsive Design
- Mobile-optimized modal
- Touch-friendly buttons
- Proper spacing and sizing
- Cross-device compatibility

## Contact Information Included

- **Name**: Fahim Hasan Santo
- **Title**: Senior Full Stack Web Developer
- **Email**: <EMAIL>
- **Phone**: +8801796680830
- **Location**: Jashore, Bangladesh
- **Social Links**: LinkedIn, Facebook, Instagram
- **Professional Skills**: PHP, JavaScript, React, WordPress

## Usage Instructions

### For Users
1. Tap the pink NFC button or scan NFC tag
2. Choose your preferred contact method
3. For VCF: File downloads automatically
4. For WhatsApp: Opens chat directly
5. Notifications confirm successful actions

### For Developers
- Logs stored in `/logs/` directory
- Contact tracking in `contact_tracking_*.log`
- NFC events in `nfc_*.log`
- Session data maintained for analytics

## Browser Compatibility
- **NFC Support**: Chrome on Android, Safari on iOS (limited)
- **Fallback**: Manual button works on all browsers
- **VCF Download**: Supported on all modern browsers
- **WhatsApp**: Works on all platforms with WhatsApp installed

## Future Enhancements
- QR code generation for contact sharing
- Multiple language support
- Advanced analytics dashboard
- Integration with CRM systems
- Custom NFC tag programming instructions
