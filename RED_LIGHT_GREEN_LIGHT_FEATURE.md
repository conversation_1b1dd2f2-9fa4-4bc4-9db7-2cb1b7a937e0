# Red Light Green Light Background Music Feature

## Overview
This feature adds the iconic Squid Game "Red Light Green Light" sound effect as background music with synchronized guard eye glow effects throughout the portfolio website.

## Features

### 🎵 **Background Music**
- **File**: `Squid Game Red Light Green Light Sound Effect - tivin01's Sound Effects.mp3`
- **Auto-start**: Begins 3 seconds after page load
- **Volume**: 30% (moderate level - not too loud, not too quiet)
- **Loop**: Continuous playback
- **Fade-in**: Smooth volume transition from 0% to 30%
- **Autoplay handling**: Graceful fallback for browsers that block autoplay

### 👁️ **Guard Eye Synchronization**
- **Target Elements**: All guard eyes (`.guard-eye`, `.guard-eye-mobile`, `.guard-eye-small`)
- **Glow Effect**: Synchronized red glow every 1.2 seconds
- **Animation**: Pulse effect with scale and box-shadow
- **Staggered**: Each eye glows with 100ms delay for visual appeal

### 🎮 **Smart Controls**
- **Visibility Handling**: Pauses when tab is hidden, resumes when visible
- **No Manual Controls**: Seamless background experience
- **Performance Optimized**: Efficient interval management

## Implementation Details

### HTML Changes
```html
<audio id="red-light-green-light" preload="auto" loop>
    <source src="assets/sounds/Squid Game Red Light Green Light Sound Effect - tivin01's Sound Effects.mp3" type="audio/mpeg">
</audio>
```

### JavaScript Features
- **Class Method**: `initBackgroundMusic()` in SquidLinkApp
- **Auto-initialization**: Called in constructor
- **Error Handling**: Graceful fallback for autoplay restrictions
- **Memory Management**: Proper cleanup of intervals

### CSS Effects
```css
.guard-eye-glow {
    background: radial-gradient(circle, #ff0055 0%, #ff3377 50%, #ff0055 100%);
    box-shadow: 0 0 30px #ff0055;
    animation: guardEyePulse 0.3s ease-in-out;
    transform: scale(1.2);
}
```

## File Requirements

### Audio File Location
Place the audio file in: `assets/sounds/`

### Exact Filename Required
`Squid Game Red Light Green Light Sound Effect - tivin01's Sound Effects.mp3`

### File Specifications
- **Format**: MP3
- **Quality**: Any quality (will be played at 30% volume)
- **Size**: Recommended under 5MB for fast loading
- **Duration**: Any length (will loop automatically)

## Browser Compatibility

### Autoplay Support
- **Chrome**: Supported with user interaction fallback
- **Firefox**: Supported with user interaction fallback
- **Safari**: Supported with user interaction fallback
- **Edge**: Supported with user interaction fallback

### Fallback Behavior
If autoplay is blocked:
1. Music will start on first user click/interaction
2. Guard eyes will begin glowing when music starts
3. No error messages or interruptions

## User Experience

### Timeline
1. **Page Load** → 3 second delay
2. **Music Starts** → Smooth fade-in to 30% volume
3. **Eyes Begin Glowing** → Synchronized with music
4. **Continuous Loop** → Background ambiance
5. **Tab Switch** → Pause/resume automatically

### Visual Effects
- Guard eyes glow with Squid Game pink (#ff0055)
- Pulse animation with scale and shadow effects
- Staggered timing for multiple eyes
- Smooth transitions and animations

## Customization Options

### Volume Adjustment
Change volume in `startBackgroundMusic()`:
```javascript
this.backgroundMusic.volume = 0.3; // 30% volume
```

### Glow Timing
Adjust glow interval in `startEyeGlowEffect()`:
```javascript
}, 1200); // Glow every 1.2 seconds
```

### Delay Before Start
Modify startup delay in `initBackgroundMusic()`:
```javascript
}, 3000); // Wait 3 seconds after page load
```

## Troubleshooting

### Music Not Playing
1. Check if audio file exists in correct location
2. Verify filename matches exactly
3. Check browser console for autoplay restrictions
4. Try clicking anywhere on the page to trigger playback

### Eyes Not Glowing
1. Verify CSS classes are applied correctly
2. Check if guard eye elements exist in DOM
3. Ensure JavaScript intervals are running
4. Inspect element styles for glow effects

### Performance Issues
1. Check audio file size (recommend under 5MB)
2. Monitor browser memory usage
3. Verify interval cleanup on page unload

## Integration Notes

- **No conflicts** with existing audio system
- **Preserves** all current sound effects (hover, NFC, fail)
- **Enhances** Squid Game theme immersion
- **Maintains** performance and user experience standards
