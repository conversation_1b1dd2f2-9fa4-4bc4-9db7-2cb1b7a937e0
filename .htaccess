# SquidLink PHP - Apache Configuration
# Optimized for shared hosting like Hostinger

# Enable rewrite engine
RewriteEngine On

# Security headers
<IfModule mod_headers.c>
    # CORS headers for API
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # PWA headers
    Header always set Service-Worker-Allowed "/"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/json "access plus 1 day"
</IfModule>

# MIME types
<IfModule mod_mime.c>
    AddType application/manifest+json .webmanifest
    AddType application/manifest+json .json
    AddType text/cache-manifest .appcache
</IfModule>

# Prevent access to sensitive files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

# API routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ index.php?action=$1 [QSA,L]

# Handle service worker
RewriteRule ^sw\.js$ sw.js [L]

# Handle manifest
RewriteRule ^manifest\.json$ manifest.json [L]

# Handle static assets
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(css|js|assets|images)/(.*)$ $0 [L]

# Main routing - everything else goes to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Error pages
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php

# PHP settings
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# Directory browsing
Options -Indexes

# Follow symbolic links
Options +FollowSymLinks

# Default charset
AddDefaultCharset UTF-8
