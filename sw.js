// SquidLink Service Worker - Enhanced Cache Management
// PWA functionality with advanced cache control and version management

const CACHE_NAME = 'squidlink-v1.0.0';
const CACHE_VERSION_KEY = 'squid_sw_version';
const API_CACHE_NAME = 'squidlink-api-v1.0.0';
const STATIC_CACHE_NAME = 'squidlink-static-v1.0.0';

// Dynamic cache version - will be updated by PHP
let DYNAMIC_CACHE_VERSION = null;

const urlsToCache = [
  '/',
  '/css/style.css',
  '/js/app.js',
  '/manifest.json',
  'https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Poppins:wght@300;400;500;600;700&display=swap',
  'https://cdn.tailwindcss.com',
  'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
];

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
};

// Install event - cache resources with version control
self.addEventListener('install', (event) => {
  console.log('SquidLink Service Worker installing...');

  event.waitUntil(
    Promise.all([
      // Cache static resources
      caches.open(STATIC_CACHE_NAME)
        .then((cache) => {
          console.log('Caching static SquidLink resources');
          return cache.addAll(urlsToCache);
        }),

      // Get current cache version from server
      fetchCacheVersion()
        .then((version) => {
          DYNAMIC_CACHE_VERSION = version;
          console.log('Cache version set to:', version);
        })
        .catch((error) => {
          console.warn('Could not fetch cache version:', error);
          DYNAMIC_CACHE_VERSION = 'fallback-' + Date.now();
        })
    ])
    .catch((error) => {
      console.error('Failed to install service worker:', error);
    })
  );

  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Fetch cache version from server
async function fetchCacheVersion() {
  try {
    const response = await fetch('/api/cache?cache_action=version');
    const data = await response.json();
    return data.version || 'v1.0.0';
  } catch (error) {
    console.warn('Failed to fetch cache version:', error);
    return 'v1.0.0';
  }
}

// Activate event - clean up old caches with version control
self.addEventListener('activate', (event) => {
  console.log('SquidLink Service Worker activating...');

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        const validCacheNames = [CACHE_NAME, STATIC_CACHE_NAME, API_CACHE_NAME];
        return Promise.all(
          cacheNames.map((cacheName) => {
            // Keep current version caches and delete old ones
            if (!validCacheNames.some(validName => cacheName.startsWith(validName.split('-v')[0]))) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),

      // Update cache version in storage
      updateCacheVersion()
    ])
  );

  // Claim all clients immediately
  self.clients.claim();
});

// Update cache version in IndexedDB or localStorage equivalent
async function updateCacheVersion() {
  try {
    if (DYNAMIC_CACHE_VERSION) {
      // Store version for future reference
      const cache = await caches.open(CACHE_NAME);
      const versionResponse = new Response(JSON.stringify({
        version: DYNAMIC_CACHE_VERSION,
        timestamp: Date.now()
      }));
      await cache.put('/__cache_version__', versionResponse);
      console.log('Cache version updated:', DYNAMIC_CACHE_VERSION);
    }
  } catch (error) {
    console.warn('Failed to update cache version:', error);
  }
}

// Enhanced fetch event with intelligent caching strategies
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  const url = new URL(event.request.url);

  // Handle different types of requests with appropriate strategies
  if (url.pathname.startsWith('/api/')) {
    // API requests - network first with cache fallback
    event.respondWith(handleApiRequest(event.request));
  } else if (isStaticAsset(url.pathname)) {
    // Static assets - cache first with network fallback
    event.respondWith(handleStaticAsset(event.request));
  } else if (url.pathname === '/' || url.pathname.endsWith('.html')) {
    // HTML pages - stale while revalidate
    event.respondWith(handleHtmlRequest(event.request));
  } else {
    // Default strategy
    event.respondWith(handleDefaultRequest(event.request));
  }
});

// Check if request is for static asset
function isStaticAsset(pathname) {
  const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2', '.ttf', '.otf'];
  return staticExtensions.some(ext => pathname.endsWith(ext));
}

// Handle API requests - Network first
async function handleApiRequest(request) {
  try {
    // Always try network first for API requests
    const networkResponse = await fetch(request);

    // Cache successful responses (except cache management API)
    if (networkResponse.ok && !request.url.includes('/api/cache')) {
      const cache = await caches.open(API_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.warn('Network failed for API request:', request.url);

    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('Serving API from cache:', request.url);
      return cachedResponse;
    }

    // Return error response
    return new Response(JSON.stringify({ error: 'Network unavailable' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle static assets - Cache first
async function handleStaticAsset(request) {
  try {
    // Check cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('Serving static asset from cache:', request.url);

      // Check if we need to revalidate based on cache version
      if (shouldRevalidate(request)) {
        // Fetch in background to update cache
        fetch(request).then(response => {
          if (response.ok) {
            caches.open(STATIC_CACHE_NAME).then(cache => {
              cache.put(request, response);
            });
          }
        }).catch(() => {
          // Ignore background fetch errors
        });
      }

      return cachedResponse;
    }

    // Not in cache, fetch from network
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache the response
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('Failed to serve static asset:', request.url, error);
    throw error;
  }
}

// Handle HTML requests - Stale while revalidate
async function handleHtmlRequest(request) {
  try {
    const cachedResponse = await caches.match(request);

    // Fetch from network in parallel
    const networkResponsePromise = fetch(request).then(response => {
      if (response.ok) {
        // Update cache with fresh content
        caches.open(CACHE_NAME).then(cache => {
          cache.put(request, response.clone());
        });
      }
      return response;
    }).catch(() => null);

    // Return cached version immediately if available
    if (cachedResponse) {
      console.log('Serving HTML from cache (stale while revalidate):', request.url);
      return cachedResponse;
    }

    // Wait for network if no cache
    const networkResponse = await networkResponsePromise;
    if (networkResponse) {
      return networkResponse;
    }

    // Fallback to offline page
    return caches.match('/') || new Response('Offline', { status: 503 });
  } catch (error) {
    console.error('Failed to serve HTML:', request.url, error);
    return caches.match('/') || new Response('Offline', { status: 503 });
  }
}

// Handle default requests
async function handleDefaultRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('Default request failed:', request.url, error);
    throw error;
  }
}

// Check if asset should be revalidated
function shouldRevalidate(request) {
  // Revalidate if cache version has changed or asset is old
  // This is a simplified check - in production you might want more sophisticated logic
  return Math.random() < 0.1; // 10% chance to revalidate
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, data } = event.data;

  switch (type) {
    case 'CLEAR_CACHE':
      handleClearCache().then(result => {
        event.ports[0].postMessage({ success: true, result });
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message });
      });
      break;

    case 'UPDATE_CACHE_VERSION':
      DYNAMIC_CACHE_VERSION = data.version;
      updateCacheVersion().then(() => {
        event.ports[0].postMessage({ success: true, version: data.version });
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message });
      });
      break;

    case 'GET_CACHE_INFO':
      getCacheInfo().then(info => {
        event.ports[0].postMessage({ success: true, info });
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message });
      });
      break;

    default:
      console.warn('Unknown message type:', type);
  }
});

// Clear all caches
async function handleClearCache() {
  try {
    const cacheNames = await caches.keys();
    const deletePromises = cacheNames.map(cacheName => {
      console.log('Deleting cache:', cacheName);
      return caches.delete(cacheName);
    });

    await Promise.all(deletePromises);
    console.log('All caches cleared');
    return { cleared: cacheNames.length };
  } catch (error) {
    console.error('Failed to clear caches:', error);
    throw error;
  }
}

// Get cache information
async function getCacheInfo() {
  try {
    const cacheNames = await caches.keys();
    const cacheInfo = [];

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();
      cacheInfo.push({
        name: cacheName,
        size: keys.length,
        urls: keys.map(request => request.url)
      });
    }

    return {
      version: DYNAMIC_CACHE_VERSION,
      caches: cacheInfo,
      totalCaches: cacheNames.length
    };
  } catch (error) {
    console.error('Failed to get cache info:', error);
    throw error;
  }
}

// Skip waiting when requested
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Background sync for contact form
self.addEventListener('sync', (event) => {
  if (event.tag === 'contact-form-sync') {
    console.log('Background sync: contact form');
    event.waitUntil(syncContactForm());
  }
});

// NFC detection background task
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'NFC_DETECTED') {
    console.log('NFC detected in background:', event.data);
    
    // Send to API
    fetch('/api/nfc', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tag_data: event.data.tagData,
        timestamp: new Date().toISOString()
      })
    }).catch(console.error);
    
    // Notify all clients
    self.clients.matchAll().then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: 'NFC_PROCESSED',
          data: event.data
        });
      });
    });
  }
});

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event);
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from SquidLink',
    icon: '/assets/icons/icon-192x192.png',
    badge: '/assets/icons/icon-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/'
    },
    actions: [
      {
        action: 'open',
        title: 'Open SquidLink',
        icon: '/assets/icons/icon-96x96.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/assets/icons/close-96x96.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('SquidLink - Player 456', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  if (event.action === 'open' || !event.action) {
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clients) => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clients) {
          if (client.url === self.registration.scope && 'focus' in client) {
            return client.focus();
          }
        }
        
        // If not, open a new window/tab
        if (self.clients.openWindow) {
          return self.clients.openWindow('/');
        }
      })
    );
  }
});

// Sync contact form data when back online
async function syncContactForm() {
  try {
    // Get pending contact form data from IndexedDB or localStorage
    // This would be implemented based on your offline storage strategy
    console.log('Syncing contact form data...');
    
    // For now, just log that sync was attempted
    return Promise.resolve();
  } catch (error) {
    console.error('Contact form sync failed:', error);
    throw error;
  }
}

// Error handler
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error);
});

// Unhandled rejection handler
self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('SquidLink Service Worker loaded successfully! 🦑');
