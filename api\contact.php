<?php
// Contact Form API - PHP 8.4.7
// Handles form submission with validation and email sending

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Validation rules
$rules = [
    'name' => ['required' => true, 'min' => 2, 'max' => 100],
    'email' => ['required' => true, 'email' => true],
    'subject' => ['required' => true, 'min' => 5, 'max' => 200],
    'message' => ['required' => true, 'min' => 10, 'max' => 1000]
];

// Get form data
$data = [
    'name' => trim($_POST['name'] ?? ''),
    'email' => trim($_POST['email'] ?? ''),
    'subject' => trim($_POST['subject'] ?? ''),
    'message' => trim($_POST['message'] ?? '')
];

// Validate data
$errors = [];

foreach ($rules as $field => $rule) {
    $value = $data[$field];
    
    // Required validation
    if ($rule['required'] && empty($value)) {
        $errors[$field] = ucfirst($field) . ' is required';
        continue;
    }
    
    // Email validation
    if (isset($rule['email']) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
        $errors[$field] = 'Please enter a valid email address';
        continue;
    }
    
    // Length validation
    if (isset($rule['min']) && strlen($value) < $rule['min']) {
        $errors[$field] = ucfirst($field) . ' must be at least ' . $rule['min'] . ' characters';
        continue;
    }
    
    if (isset($rule['max']) && strlen($value) > $rule['max']) {
        $errors[$field] = ucfirst($field) . ' must not exceed ' . $rule['max'] . ' characters';
        continue;
    }
}

// Return validation errors
if (!empty($errors)) {
    echo json_encode(['success' => false, 'errors' => $errors]);
    exit;
}

// Sanitize data
$data = array_map('htmlspecialchars', $data);

// Email configuration
$to = '<EMAIL>'; // Replace with your email
$from = $data['email'];
$reply_to = $data['email'];

// Email headers
$headers = [
    'From: ' . $data['name'] . ' <' . $from . '>',
    'Reply-To: ' . $reply_to,
    'X-Mailer: PHP/' . phpversion(),
    'MIME-Version: 1.0',
    'Content-Type: text/html; charset=UTF-8'
];

// Email subject
$email_subject = '[SquidLink Contact] ' . $data['subject'];

// Email body
$email_body = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ff0055, #00ffaa); color: white; padding: 20px; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #ff0055; }
        .value { margin-top: 5px; padding: 10px; background: white; border-left: 4px solid #00ffaa; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦑 New Contact from SquidLink</h1>
            <p>Player 456 Portfolio Contact Form</p>
        </div>
        
        <div class="content">
            <div class="field">
                <div class="label">Name:</div>
                <div class="value">' . $data['name'] . '</div>
            </div>
            
            <div class="field">
                <div class="label">Email:</div>
                <div class="value">' . $data['email'] . '</div>
            </div>
            
            <div class="field">
                <div class="label">Subject:</div>
                <div class="value">' . $data['subject'] . '</div>
            </div>
            
            <div class="field">
                <div class="label">Message:</div>
                <div class="value">' . nl2br($data['message']) . '</div>
            </div>
        </div>
        
        <div class="footer">
            <p>This message was sent from your SquidLink portfolio contact form.</p>
            <p>Sent on: ' . date('Y-m-d H:i:s') . '</p>
        </div>
    </div>
</body>
</html>';

// Try to send email
$mail_sent = false;

// Method 1: Try PHP mail() function
if (function_exists('mail')) {
    $mail_sent = mail($to, $email_subject, $email_body, implode("\r\n", $headers));
}

// Method 2: Save to file if mail() fails (for development/testing)
if (!$mail_sent) {
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/contact_' . date('Y-m-d') . '.log';
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => $data,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    file_put_contents($log_file, json_encode($log_entry, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);
    $mail_sent = true; // Consider it sent for development
}

// Response
if ($mail_sent) {
    // Log successful submission
    error_log("SquidLink contact form submitted by: " . $data['name'] . " (" . $data['email'] . ")");
    
    echo json_encode([
        'success' => true,
        'message' => 'Thank you! Your message has been sent successfully. I\'ll get back to you soon!'
    ]);
} else {
    error_log("SquidLink contact form failed to send email for: " . $data['name'] . " (" . $data['email'] . ")");
    
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error sending your message. Please try again later or contact me directly.'
    ]);
}
?>
