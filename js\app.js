// SquidLink PHP - Main JavaScript
// Exact match to React version functionality

class SquidLinkApp {
    constructor() {
        this.currentName = 'FAHIM';
        this.nameIndex = 0;
        this.names = ['FAHIM', 'HASAN', 'SANTO'];
        this.isRedirecting = false;
        this.redirectingTo = '';
        this.playerNumber = 456;
        this.audioContext = null;
        this.sounds = {};
        this.socialUrls = {
            'Facebook': 'https://www.instagram.com/_fahimsanto/',
            'LinkedIn': 'https://www.linkedin.com/in/fahim-hasan-santo-583987267/',
            'Instagram': 'https://www.facebook.com/fahim.hasan.santo.2024'
        };

        // Preloader and Haptic System
        this.hapticSupported = false;
        this.preloaderActive = true;
        this.introActive = true;
        this.loadingSteps = ['fonts', 'styles', 'scripts', 'assets', 'ready'];
        this.currentStep = 0;
        this.loadingProgress = 0;
        this.cacheVersion = window.SQUID_CACHE_VERSION || 'v1.0.0';

        this.initMobileOptimizations();
        this.initNameCycling();
        this.initIntroAnimation();
        this.initNFCModal();
        this.initBackgroundMusic();
        this.init();
    }

    initNameCycling() {
        const names = ['FAHIM', 'HASAN', 'SANTO'];
        const nameElement = document.getElementById('name-text');
        const glitchElement = document.getElementById('glitch-name');

        if (!nameElement || !glitchElement) return;

        let currentIndex = 0;

        const cycleName = () => {
            // Add glitch effect
            glitchElement.classList.add('glitch-active');

            // Change name after glitch starts
            setTimeout(() => {
                currentIndex = (currentIndex + 1) % names.length;
                const newName = names[currentIndex];

                // Fade out current name with scale
                nameElement.style.opacity = '0.3';
                nameElement.style.transform = 'scale(0.95)';

                setTimeout(() => {
                    nameElement.textContent = newName;
                    glitchElement.setAttribute('data-text', newName);

                    // Fade in new name with scale
                    nameElement.style.opacity = '1';
                    nameElement.style.transform = 'scale(1)';

                    // Remove glitch effect
                    setTimeout(() => {
                        glitchElement.classList.remove('glitch-active');
                    }, 600);
                }, 200);

            }, 300);
        };

        // Start cycling after intro animation completes
        setTimeout(() => {
            console.log('Starting name cycling animation...');
            cycleName();
            // Continue cycling every 4 seconds
            setInterval(cycleName, 4000);
        }, 6000); // Wait for intro to complete
    }

    initMobileOptimizations() {
        // Detect mobile device
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isSmallScreen = window.innerWidth <= 768;

        if (isMobile || isSmallScreen) {
            document.body.classList.add('mobile-device');

            // Add mobile-specific optimizations
            this.optimizeForMobile();
        }

        // Handle orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        // Handle resize for responsive behavior
        window.addEventListener('resize', () => {
            const isNowSmallScreen = window.innerWidth <= 768;
            if (isNowSmallScreen && !document.body.classList.contains('mobile-device')) {
                document.body.classList.add('mobile-device');
                this.optimizeForMobile();
            } else if (!isNowSmallScreen && document.body.classList.contains('mobile-device')) {
                document.body.classList.remove('mobile-device');
            }
        });
    }

    optimizeForMobile() {
        // Optimize intro animation for mobile
        const introShapes = document.querySelectorAll('.intro-shape');
        introShapes.forEach((shape, index) => {
            // Reduce animation delay for faster mobile experience
            shape.style.animationDelay = `${0.8 + (index * 0.2)}s`;
        });

        // Optimize navigation for mobile
        const navContainer = document.querySelector('.nav-container');
        if (navContainer && window.innerWidth <= 480) {
            navContainer.style.flexDirection = 'column';
            navContainer.style.gap = '0.5rem';
        }
    }

    handleOrientationChange() {
        // Force a repaint to fix any layout issues after orientation change
        const intro = document.getElementById('squid-intro');
        if (intro && intro.style.display !== 'none') {
            intro.style.display = 'none';
            intro.offsetHeight; // Force reflow
            intro.style.display = 'flex';
        }

        // Recalculate navigation layout
        this.optimizeForMobile();
    }

    initIntroAnimation() {
        // Start intro animation sequence
        const intro = document.getElementById('squid-intro');
        if (!intro) {
            // If intro element not found, skip to preloader
            this.initPreloader();
            return;
        }

        // Auto-complete intro after 2.5 seconds (reduced delay)
        setTimeout(() => {
            this.completeIntroAnimation();
        }, 2500);

        // Allow click to skip intro
        intro.addEventListener('click', () => {
            this.completeIntroAnimation();
        });

        // Allow escape key to skip intro
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.introActive) {
                this.completeIntroAnimation();
            }
        });
    }

    completeIntroAnimation() {
        if (!this.introActive) return;

        this.introActive = false;
        const intro = document.getElementById('squid-intro');

        if (intro) {
            // Fade out intro
            intro.classList.add('fade-out');

            setTimeout(() => {
                intro.style.display = 'none';
                // Show and start preloader
                const preloader = document.getElementById('squid-preloader');
                if (preloader) {
                    preloader.style.display = 'flex';
                }
                this.initPreloader();
            }, 600); // Reduced delay
        } else {
            // Fallback if intro element not found
            this.initPreloader();
        }
    }

    initPreloader() {
        // Start preloader immediately
        this.setupHapticFeedback();
        this.startPreloadingSequence();
    }

    init() {
        this.setupCursor();
        this.setupScrollProgress();
        this.setupParticles();
        this.setupNameCycling();
        this.setupProjectRedirects();
        this.setupSocialRedirects();
        // this.setupNFC(); // Disabled NFC alerts
        this.setupAudio();
        this.updatePlayerNumber();
        // this.setupHoverSounds(); // Disabled sound effects
        this.setupNavigationSymbols();
        this.setupDigitalClock();
        this.setupSmoothScrolling();
        this.setupMobileOptimizations();
        this.setupScrollAnimation();
        this.setupScrollReveal();
        this.setupCacheManagement();
    }

    setupCursor() {
        // Only setup custom cursor on non-mobile devices
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        if (!isMobile) {
            const cursor = document.createElement('div');
            cursor.className = 'squid-cursor';
            document.body.appendChild(cursor);

            document.addEventListener('mousemove', (e) => {
                cursor.style.left = e.clientX + 'px';
                cursor.style.top = e.clientY + 'px';
            });

            // Cursor hover effects
            const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card');
            hoverElements.forEach(el => {
                el.addEventListener('mouseenter', () => {
                    cursor.style.transform = 'scale(1.5)';
                    this.playSound('hover');
                });
                el.addEventListener('mouseleave', () => {
                    cursor.style.transform = 'scale(1)';
                });
            });
        }
    }

    setupNavigationSymbols() {
        const symbols = document.querySelectorAll('nav .text-squid-pink span');
        symbols.forEach(symbol => {
            symbol.addEventListener('click', () => {
                // this.playSound('hover'); // Disabled sound effects
                symbol.style.animation = 'glitch 0.5s ease-in-out';
                setTimeout(() => {
                    symbol.style.animation = '';
                }, 500);
            });
        });
    }

    setupScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        document.body.appendChild(progressBar);

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });
    }

    setupParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 50 },
                    color: { value: ['#ff0055', '#00ffaa', '#ffffff'] },
                    shape: {
                        type: ['circle', 'triangle', 'polygon'],
                        polygon: { nb_sides: 6 }
                    },
                    opacity: {
                        value: 0.3,
                        random: true
                    },
                    size: {
                        value: 3,
                        random: true,
                        anim: {
                            enable: true,
                            speed: 2,
                            size_min: 0.3
                        }
                    },
                    move: {
                        enable: true,
                        speed: 1,
                        direction: 'top',
                        random: true,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: {
                            enable: true,
                            mode: 'repulse'
                        },
                        onclick: {
                            enable: true,
                            mode: 'push'
                        }
                    },
                    modes: {
                        repulse: {
                            distance: 100,
                            duration: 0.4
                        },
                        push: {
                            particles_nb: 4
                        }
                    }
                },
                retina_detect: true
            });
        }
    }

    setupNameCycling() {
        const nameElement = document.querySelector('.glitch-text');
        if (nameElement) {
            setInterval(() => {
                this.nameIndex = (this.nameIndex + 1) % this.names.length;
                this.currentName = this.names[this.nameIndex];
                nameElement.textContent = this.currentName;
                nameElement.setAttribute('data-text', this.currentName);
            }, 3000);
        }
    }

    setupProjectRedirects() {
        const projectCards = document.querySelectorAll('.project-card');
        console.log('Found project cards:', projectCards.length);

        // Also add direct click handlers to VIEW PROJECT buttons
        const viewProjectButtons = document.querySelectorAll('.project-card .bg-white\\/20');
        console.log('Found VIEW PROJECT buttons:', viewProjectButtons.length);

        projectCards.forEach((card, index) => {
            const projectUrl = card.getAttribute('data-url');
            const projectName = card.getAttribute('data-name');
            console.log(`Project ${index + 1}: ${projectName} - ${projectUrl}`);
            let touchStartTime = 0;
            let touchStartY = 0;
            let touchStartX = 0;
            let isScrolling = false;

            // Handle touch start to detect scrolling vs clicking
            const handleTouchStart = (e) => {
                touchStartTime = Date.now();
                touchStartY = e.touches[0].clientY;
                touchStartX = e.touches[0].clientX;
                isScrolling = false;
            };

            // Handle touch move to detect if user is scrolling
            const handleTouchMove = (e) => {
                if (!touchStartTime) return;

                const touchY = e.touches[0].clientY;
                const touchX = e.touches[0].clientX;
                const deltaY = Math.abs(touchY - touchStartY);
                const deltaX = Math.abs(touchX - touchStartX);

                // If user moved more than 10px in any direction, consider it scrolling
                if (deltaY > 10 || deltaX > 10) {
                    isScrolling = true;
                }
            };

            // Handle actual click/tap
            const handleCardClick = (e) => {
                // For touch events, check if it was a scroll or a tap
                if (e.type === 'touchend') {
                    const touchDuration = Date.now() - touchStartTime;

                    // Ignore if:
                    // - Touch was too short (< 50ms) - likely accidental
                    // - Touch was too long (> 500ms) - likely a long press
                    // - User was scrolling
                    if (touchDuration < 50 || touchDuration > 500 || isScrolling) {
                        return;
                    }
                }

                e.preventDefault();
                e.stopPropagation();

                // Get project data from card attributes
                const projectUrl = card.getAttribute('data-url');
                const projectName = card.getAttribute('data-name');

                console.log('Project card clicked:', projectName, projectUrl);

                if (projectUrl && projectName) {
                    this.handleProjectClick({ name: projectName, url: projectUrl });
                } else {
                    console.error('Missing project data:', { projectUrl, projectName });
                }
            };

            // Add simple click handler for testing
            const simpleClickHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();

                const projectUrl = card.getAttribute('data-url');
                const projectName = card.getAttribute('data-name');

                console.log('Simple click handler:', projectName, projectUrl);

                if (projectUrl && projectName) {
                    this.handleProjectClick({ name: projectName, url: projectUrl });
                }
            };

            // Add event listeners
            card.addEventListener('touchstart', handleTouchStart, { passive: true });
            card.addEventListener('touchmove', handleTouchMove, { passive: true });
            card.addEventListener('touchend', handleCardClick, { passive: false });
            card.addEventListener('click', simpleClickHandler);

            // Prevent context menu on long press
            card.addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });

            // Add mobile-friendly styling
            card.style.cursor = 'pointer';
            card.style.webkitTapHighlightColor = 'transparent';
            card.style.userSelect = 'none';
            card.style.webkitUserSelect = 'none';
        });

        // Add direct click handlers to VIEW PROJECT buttons as backup
        viewProjectButtons.forEach((button, index) => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const card = button.closest('.project-card');
                if (card) {
                    const projectUrl = card.getAttribute('data-url');
                    const projectName = card.getAttribute('data-name');

                    console.log('VIEW PROJECT button clicked:', projectName, projectUrl);

                    if (projectUrl && projectName) {
                        // Direct redirect without loading animation for testing
                        window.open(projectUrl, '_blank');
                    }
                }
            });

            button.style.cursor = 'pointer';
            button.style.pointerEvents = 'auto';
        });
    }

    handleProjectClick(project) {
        console.log('handleProjectClick called with:', project);

        if (this.isRedirecting) {
            console.log('Already redirecting, ignoring click');
            return;
        }

        this.isRedirecting = true;
        this.redirectingTo = project.name;
        this.playSound('hover');

        console.log('Showing loading for:', project.name);
        this.showPremiumLoading(project.name);

        setTimeout(() => {
            console.log('Opening URL:', project.url);
            window.open(project.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    setupSocialRedirects() {
        const socialLinks = document.querySelectorAll('.social-link');
        const socials = [
            { name: 'Instagram', url: 'https://www.instagram.com/_fahimsanto/' },
            { name: 'LinkedIn', url: 'https://www.linkedin.com/fahim-hasan-santo-583987267/' },
            { name: 'Facebook', url: 'https://www.facebook.com/fahim.hasan.santo.2024' }
        ];

        socialLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (socials[index]) {
                    this.handleSocialClick(socials[index]);
                }
            });
        });
    }

    handleSocialClick(social) {
        if (this.isRedirecting) return;

        this.isRedirecting = true;
        this.redirectingTo = social.name;
        this.playSound('hover');

        this.showPremiumLoading(social.name);

        setTimeout(() => {
            window.open(social.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    showPremiumLoading(destination) {
        const overlay = document.createElement('div');
        overlay.className = 'premium-loading-overlay';
        overlay.innerHTML = `
            <!-- Corner Circles -->
            <div class="corner-circle top-left"></div>
            <div class="corner-circle top-right"></div>
            <div class="corner-circle bottom-left"></div>
            <div class="corner-circle bottom-right"></div>

            <div class="loading-content">
                <div class="loading-logo">
                    <div class="squid-logo">
                        <div class="logo-circle"></div>
                        <div class="logo-triangle"></div>
                        <div class="logo-square"></div>
                    </div>
                </div>
                <div class="loading-text">REDIRECTING TO</div>
                <div class="loading-destination">${destination.toUpperCase()}</div>
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    hidePremiumLoading() {
        const overlay = document.querySelector('.premium-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    setupNFC() {
        const nfcButton = document.querySelector('.nfc-button');
        if (nfcButton) {
            // Handle both click and touch events
            const handleNFCTrigger = (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.triggerNFCAnimation();
            };

            // Add multiple event listeners for better mobile support
            nfcButton.addEventListener('click', handleNFCTrigger);
            nfcButton.addEventListener('touchstart', handleNFCTrigger, { passive: false });

            // Prevent touch issues on mobile
            nfcButton.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, { passive: false });

            nfcButton.addEventListener('touchmove', (e) => {
                e.preventDefault();
            }, { passive: false });
        }
    }

    triggerNFCAnimation() {
        // Create NFC animation overlay
        const nfcOverlay = document.createElement('div');
        nfcOverlay.className = 'nfc-animation-overlay';
        nfcOverlay.innerHTML = `
            <div class="nfc-content">
                <div class="nfc-card">📱 NFC CARD DETECTED</div>
                <div class="nfc-player">456 VERIFIED</div>
                <div class="nfc-status">ACCESS GRANTED</div>
            </div>
        `;
        document.body.appendChild(nfcOverlay);

        setTimeout(() => {
            nfcOverlay.remove();
        }, 4000);
    }

    setupAudio() {
        // Audio disabled
        console.log('Audio effects disabled');
    }

    playSound(type) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        if (type === 'hover') {
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
        }

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    setupHoverSounds() {
        const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card, .project-card');
        hoverElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                this.playSound('hover');
            });
        });
    }

    updatePlayerNumber() {
        const playerNumberElement = document.querySelector('.digital-display');
        if (playerNumberElement) {
            playerNumberElement.textContent = `${this.playerNumber}`;
        }
    }

    setupDigitalClock() {
        const updateClock = () => {
            const now = new Date();

            // Format time
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            const timeString = `${hours}:${minutes}:${seconds}`;

            // Format date
            const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                           'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

            const dayName = days[now.getDay()];
            const monthName = months[now.getMonth()];
            const date = now.getDate().toString().padStart(2, '0');
            const dateString = `${dayName} ${monthName} ${date}`;

            // Update display
            const timeElement = document.getElementById('digital-time');
            const dateElement = document.getElementById('digital-date');

            if (timeElement) timeElement.textContent = timeString;
            if (dateElement) dateElement.textContent = dateString;
        };

        // Update immediately and then every second
        updateClock();
        setInterval(updateClock, 1000);
    }

    setupSmoothScrolling() {
        // Handle navigation links
        const navLinks = document.querySelectorAll('nav a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupMobileOptimizations() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        if (isMobile) {
            // Add mobile class to body
            document.body.classList.add('mobile-device');

            // Disable hover effects on mobile
            const hoverElements = document.querySelectorAll('.card-3d, .project-card, .skill-card');
            hoverElements.forEach(el => {
                el.style.transition = 'transform 0.2s ease';
            });

            // Improve touch targets
            const touchTargets = document.querySelectorAll('button, a, .nfc-button');
            touchTargets.forEach(target => {
                target.style.minHeight = '44px';
                target.style.minWidth = '44px';
                target.style.webkitTapHighlightColor = 'transparent';
            });

            // Fix viewport issues
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }

            // Prevent zoom on input focus
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });
            });
        }
    }

    setupScrollAnimation() {
        const nav = document.getElementById('main-nav');
        let lastScrollTop = 0;
        let ticking = false;
        let isScrollingDown = false;
        let scrollTimeout;

        const updateNavigation = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up';

            // Clear previous timeout
            clearTimeout(scrollTimeout);

            // Remove all classes first
            nav.classList.remove('initial', 'hidden', 'visible', 'scrolled');

            if (scrollTop <= 50) {
                // At the very top - show normal navigation
                nav.classList.add('initial');
            } else if (scrollTop > 50 && scrollTop <= 150) {
                // Small scroll - show compact navigation
                nav.classList.add('visible', 'scrolled');
            } else {
                // Deeper scroll - show/hide based on direction
                if (scrollDirection === 'down' && scrollTop > lastScrollTop + 5) {
                    // Scrolling down - hide navigation with delay
                    isScrollingDown = true;
                    scrollTimeout = setTimeout(() => {
                        if (isScrollingDown) {
                            nav.classList.add('hidden');
                        }
                    }, 150);
                } else if (scrollDirection === 'up' || scrollTop < lastScrollTop - 5) {
                    // Scrolling up - show navigation immediately
                    isScrollingDown = false;
                    nav.classList.add('visible', 'scrolled');
                }
            }

            lastScrollTop = scrollTop;
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavigation);
                ticking = true;
            }
        };

        // Listen for scroll events
        window.addEventListener('scroll', requestTick, { passive: true });

        // Handle scroll end - show navigation after scroll stops
        let scrollEndTimer;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollEndTimer);
            scrollEndTimer = setTimeout(() => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                if (scrollTop > 150) {
                    nav.classList.remove('hidden');
                    nav.classList.add('visible', 'scrolled');
                }
            }, 1000); // Show navigation 1 second after scroll stops
        }, { passive: true });

        // Initial state
        nav.classList.add('initial');
    }

    // SCROLL REVEAL ANIMATIONS
    setupScrollReveal() {
        // Create intersection observer for scroll reveal
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    // this.playSound('hover'); // Disabled sound effects
                }
            });
        }, observerOptions);

        // Observe all scroll reveal elements
        const revealElements = document.querySelectorAll(
            '.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, ' +
            '.scroll-reveal-scale, .scroll-reveal-fade, .scroll-reveal-stagger, ' +
            '.squid-reveal, .glitch-reveal'
        );

        revealElements.forEach(element => {
            observer.observe(element);
        });

        // Special handling for staggered elements
        const staggerElements = document.querySelectorAll('.scroll-reveal-stagger');
        if (staggerElements.length > 0) {
            const staggerObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.classList.add('revealed');
                            this.playSound('hover');
                        }, index * 100); // Stagger delay
                    }
                });
            }, observerOptions);

            staggerElements.forEach(element => {
                staggerObserver.observe(element);
            });
        }
    }

    // PRELOADER SYSTEM
    setupHapticFeedback() {
        // Check for haptic feedback support
        this.hapticSupported = 'vibrate' in navigator ||
                              ('hapticActuators' in navigator && navigator.hapticActuators.length > 0);

        const hapticIndicator = document.querySelector('.haptic-indicator');
        if (hapticIndicator) {
            if (!this.hapticSupported) {
                hapticIndicator.classList.add('disabled');
                hapticIndicator.querySelector('.haptic-text').textContent = 'Haptic feedback not supported';
            }
        }
    }

    triggerHapticFeedback(pattern = [100]) {
        if (this.hapticSupported && navigator.vibrate) {
            navigator.vibrate(pattern);
        }
    }

    startPreloadingSequence() {
        const preloader = document.getElementById('squid-preloader');
        if (!preloader) return;

        // Simulate loading steps
        this.simulateLoading();

        // Listen for actual resource loading
        this.trackResourceLoading();

        // Auto-hide after maximum time (reduced)
        setTimeout(() => {
            if (this.preloaderActive) {
                this.completePreloading();
            }
        }, 5000); // Maximum 5 seconds (reduced from 8)
    }

    simulateLoading() {
        const steps = this.loadingSteps;
        const progressBar = document.querySelector('.progress-fill');
        const progressText = document.getElementById('progress-percentage');
        const statusText = document.getElementById('progress-status');

        let currentStep = 0;
        const stepDuration = 600; // Reduced to 0.6 seconds per step

        const updateStep = () => {
            if (currentStep < steps.length) {
                // Update step indicators
                const stepElements = document.querySelectorAll('.step');

                // Mark previous steps as completed
                for (let i = 0; i < currentStep; i++) {
                    stepElements[i]?.classList.add('completed');
                    stepElements[i]?.classList.remove('active');
                }

                // Mark current step as active
                if (stepElements[currentStep]) {
                    stepElements[currentStep].classList.add('active');
                    stepElements[currentStep].classList.remove('completed');
                }

                // Update progress
                const progress = ((currentStep + 1) / steps.length) * 100;
                this.loadingProgress = progress;

                if (progressBar) progressBar.style.width = progress + '%';
                if (progressText) progressText.textContent = Math.round(progress) + '%';

                // Update status text
                const statusMessages = {
                    'fonts': 'Loading Squid Game fonts...',
                    'styles': 'Applying premium styles...',
                    'scripts': 'Initializing game mechanics...',
                    'assets': 'Loading game assets...',
                    'ready': 'Welcome to the game!'
                };

                if (statusText) {
                    statusText.textContent = statusMessages[steps[currentStep]] || 'Loading...';
                }

                // Trigger haptic feedback for each step
                this.triggerHapticFeedback([50]);

                currentStep++;

                if (currentStep < steps.length) {
                    setTimeout(updateStep, stepDuration);
                } else {
                    // All steps completed
                    setTimeout(() => {
                        this.completePreloading();
                    }, 500); // Reduced delay
                }
            }
        };

        // Start the sequence immediately
        setTimeout(updateStep, 200); // Reduced initial delay
    }

    trackResourceLoading() {
        // Track actual resource loading
        const resources = [
            '/css/style.css' + (window.SQUID_CACHE_BUST || ''),
            '/js/app.js' + (window.SQUID_CACHE_BUST || ''),
            'https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Poppins:wght@300;400;500;600;700&display=swap'
        ];

        let loadedCount = 0;
        const totalResources = resources.length;

        resources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = resource.includes('.css') ? 'style' : 'script';
            link.href = resource;

            link.onload = () => {
                loadedCount++;
                if (loadedCount === totalResources) {
                    // All resources loaded
                    setTimeout(() => {
                        if (this.preloaderActive) {
                            this.completePreloading();
                        }
                    }, 500);
                }
            };

            link.onerror = () => {
                loadedCount++;
                if (loadedCount === totalResources) {
                    setTimeout(() => {
                        if (this.preloaderActive) {
                            this.completePreloading();
                        }
                    }, 500);
                }
            };

            document.head.appendChild(link);
        });
    }

    completePreloading() {
        if (!this.preloaderActive) return;

        this.preloaderActive = false;
        const preloader = document.getElementById('squid-preloader');

        if (preloader) {
            // Final haptic feedback
            this.triggerHapticFeedback([100, 50, 100]);

            // Fade out animation
            preloader.classList.add('fade-out');

            setTimeout(() => {
                preloader.classList.add('hidden');
                // Initialize main app
                this.init();
            }, 400); // Reduced fade out delay
        } else {
            // Fallback if preloader element not found
            this.init();
        }
    }

    // CACHE MANAGEMENT SYSTEM
    setupCacheManagement() {
        // Listen for cache management key combination (Ctrl+Shift+C)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.showCacheManagement();
            }
        });

        // Check cache version on load
        this.checkCacheVersion();
    }

    checkCacheVersion() {
        // Compare current version with stored version
        const storedVersion = localStorage.getItem('squid_cache_version');
        const currentVersion = this.cacheVersion;

        if (storedVersion && storedVersion !== currentVersion) {
            console.log('Cache version changed:', storedVersion, '->', currentVersion);
            // Clear any client-side cache if needed
            this.clearClientCache();
        }

        localStorage.setItem('squid_cache_version', currentVersion);
    }

    clearClientCache() {
        // Clear localStorage cache
        const keysToKeep = ['squid_cache_version'];
        Object.keys(localStorage).forEach(key => {
            if (!keysToKeep.includes(key) && key.startsWith('squid_')) {
                localStorage.removeItem(key);
            }
        });

        // Clear sessionStorage
        sessionStorage.clear();

        console.log('Client cache cleared');
    }

    showCacheManagement() {
        const modal = document.createElement('div');
        modal.className = 'cache-management-modal';
        modal.innerHTML = `
            <div class="cache-modal-content">
                <h3>🦑 Cache Management</h3>
                <p>Current Version: ${this.cacheVersion}</p>
                <div class="cache-buttons">
                    <button onclick="squidApp.clearCache()" class="cache-btn clear">Clear Cache</button>
                    <button onclick="squidApp.restoreCache()" class="cache-btn restore">Restore Cache</button>
                    <button onclick="squidApp.forceRefresh()" class="cache-btn refresh">Force Refresh</button>
                    <button onclick="squidApp.closeCacheModal()" class="cache-btn close">Close</button>
                </div>
            </div>
        `;

        // Add modal styles
        const style = document.createElement('style');
        style.textContent = `
            .cache-management-modal {
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 20000; display: flex;
                justify-content: center; align-items: center;
            }
            .cache-modal-content {
                background: #111; padding: 2rem; border-radius: 10px;
                border: 2px solid rgb(255, 0, 85); color: white; text-align: center;
            }
            .cache-buttons { display: flex; gap: 1rem; margin-top: 1rem; flex-wrap: wrap; }
            .cache-btn {
                padding: 0.5rem 1rem; border: 1px solid rgb(255, 0, 85);
                background: transparent; color: rgb(255, 0, 85); cursor: pointer;
                border-radius: 5px; transition: all 0.3s ease;
            }
            .cache-btn:hover { background: rgb(255, 0, 85); color: white; }
        `;

        document.head.appendChild(style);
        document.body.appendChild(modal);

        // Store reference for cleanup
        this.cacheModal = modal;
        this.cacheModalStyle = style;
    }

    async clearCache() {
        try {
            const response = await fetch('/api/cache?cache_action=clear&key=' + this.getDailyCacheKey(), {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                alert('Cache cleared successfully! Page will reload.');
                window.location.reload();
            } else {
                alert('Failed to clear cache: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Cache clear error:', error);
            alert('Error clearing cache. Check console for details.');
        }
    }

    async restoreCache() {
        try {
            const response = await fetch('/api/cache?cache_action=restore&key=' + this.getDailyCacheKey(), {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                alert('Cache restored successfully! Page will reload.');
                window.location.reload();
            } else {
                alert('Failed to restore cache: ' + (result.error || 'No backup available'));
            }
        } catch (error) {
            console.error('Cache restore error:', error);
            alert('Error restoring cache. Check console for details.');
        }
    }

    async forceRefresh() {
        try {
            const response = await fetch('/api/cache?cache_action=force_refresh&key=' + this.getDailyCacheKey(), {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                alert('Force refresh completed! Page will reload.');
                // Clear service worker cache
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                }
                window.location.reload(true);
            } else {
                alert('Failed to force refresh: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Force refresh error:', error);
            alert('Error during force refresh. Check console for details.');
        }
    }

    closeCacheModal() {
        if (this.cacheModal) {
            this.cacheModal.remove();
            this.cacheModal = null;
        }
        if (this.cacheModalStyle) {
            this.cacheModalStyle.remove();
            this.cacheModalStyle = null;
        }
    }

    getDailyCacheKey() {
        const today = new Date().toISOString().split('T')[0];
        return 'squid456_cache_key_' + today;
    }

    // NFC Modal System
    initNFCModal() {
        const nfcButton = document.getElementById('nfc-tap-button');
        const nfcModal = document.getElementById('nfc-contact-modal');
        const closeModalBtn = document.getElementById('close-nfc-modal');
        const viewPortfolioBtn = document.getElementById('view-portfolio-btn');

        if (!nfcButton || !nfcModal) return;

        // NFC Button Click Handler
        nfcButton.addEventListener('click', () => {
            this.showNFCModal();
            this.logNFCTap();
            this.playSound('nfc');
            this.triggerHapticFeedback();
        });

        // Close Modal Handler
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                this.hideNFCModal();
            });
        }

        // View Portfolio Handler
        if (viewPortfolioBtn) {
            viewPortfolioBtn.addEventListener('click', () => {
                this.hideNFCModal();
                // Smooth scroll to about section
                document.getElementById('about')?.scrollIntoView({
                    behavior: 'smooth'
                });
            });
        }

        // Close modal when clicking outside
        nfcModal.addEventListener('click', (e) => {
            if (e.target === nfcModal) {
                this.hideNFCModal();
            }
        });

        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !nfcModal.classList.contains('hidden')) {
                this.hideNFCModal();
            }
        });

        // Track VCF download
        const vcfButton = nfcModal.querySelector('.vcf-btn');
        if (vcfButton) {
            vcfButton.addEventListener('click', () => {
                this.trackContactAction('vcf_download');
                this.playSound('hover');
                this.showNotification('🧩', 'Contact saved to your device!');
                setTimeout(() => this.hideNFCModal(), 1500);
            });
        }

        // Track WhatsApp clicks
        const whatsappButtons = document.querySelectorAll('.whatsapp-btn, .floating-whatsapp');
        whatsappButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.trackContactAction('whatsapp_click');
                this.playSound('hover');
                this.showNotification('💬', 'Opening WhatsApp...');
            });
        });

        // Auto-detect NFC if supported
        this.detectNFCSupport();
    }

    showNFCModal() {
        const modal = document.getElementById('nfc-contact-modal');
        if (modal) {
            modal.classList.remove('hidden');
            // Add animation class
            modal.style.animation = 'fadeInScale 0.3s ease-out';

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            console.log('NFC Contact Modal opened');
        }
    }

    hideNFCModal() {
        const modal = document.getElementById('nfc-contact-modal');
        if (modal) {
            modal.style.animation = 'fadeOutScale 0.3s ease-in';

            setTimeout(() => {
                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }, 300);

            console.log('NFC Contact Modal closed');
        }
    }

    async logNFCTap() {
        try {
            const response = await fetch('/?action=nfc', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tag_data: 'manual_tap',
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent
                })
            });

            const result = await response.json();
            console.log('NFC tap logged:', result);
        } catch (error) {
            console.error('Failed to log NFC tap:', error);
        }
    }

    detectNFCSupport() {
        if ('NDEFReader' in window) {
            console.log('NFC is supported on this device');
            this.initRealNFC();
        } else {
            console.log('NFC not supported, using manual tap simulation');
        }
    }

    async initRealNFC() {
        try {
            const ndef = new NDEFReader();
            await ndef.scan();
            console.log('NFC scan started');

            ndef.addEventListener('reading', ({ message, serialNumber }) => {
                console.log('NFC tag detected:', serialNumber);
                this.showNFCModal();
                this.logNFCTap();
                this.playSound('nfc');
                this.triggerHapticFeedback();
            });

        } catch (error) {
            console.log('NFC scan failed:', error);
        }
    }

    triggerHapticFeedback() {
        if (navigator.vibrate) {
            // Squid Game style vibration pattern
            navigator.vibrate([100, 50, 100, 50, 200]);
        }
    }

    async trackContactAction(action) {
        try {
            const response = await fetch('/?action=contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action,
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent,
                    source: 'nfc_modal'
                })
            });

            const result = await response.json();
            console.log('Contact action tracked:', action, result);
        } catch (error) {
            console.error('Failed to track contact action:', error);
        }
    }

    showNotification(icon, message) {
        const toast = document.getElementById('notification-toast');
        const iconElement = document.getElementById('notification-icon');
        const messageElement = document.getElementById('notification-message');

        if (toast && iconElement && messageElement) {
            iconElement.textContent = icon;
            messageElement.textContent = message;

            // Show notification
            toast.classList.remove('hidden');
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // Hide notification after 3 seconds
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    toast.classList.add('hidden');
                }, 300);
            }, 3000);
        }
    }

    // Background Music System
    initBackgroundMusic() {
        this.backgroundMusic = null;
        this.guardEyes = [];
        this.musicStarted = false;
        this.eyeGlowInterval = null;

        // Start background music after page loads
        setTimeout(() => {
            this.startBackgroundMusic();
        }, 3000); // Wait 3 seconds after page load

        // Handle page visibility changes
        this.setupVisibilityHandling();
    }

    startBackgroundMusic() {
        if (this.musicStarted) return;

        this.backgroundMusic = document.getElementById('red-light-green-light');
        if (!this.backgroundMusic) {
            console.log('Red Light Green Light audio element not found');
            return;
        }

        console.log('Found audio element, attempting to start music...');
        console.log('Audio source:', this.backgroundMusic.src);
        console.log('Current URL:', window.location.href);

        // Set volume to moderate level (not too loud, not too quiet)
        this.backgroundMusic.volume = 0.3;

        // Get all guard eyes
        this.guardEyes = document.querySelectorAll('.guard-eye, .guard-eye-mobile, .guard-eye-small');
        console.log('Found guard eyes:', this.guardEyes.length);

        // Add error handling for audio loading
        this.backgroundMusic.addEventListener('error', (e) => {
            console.error('Audio loading error:', e);
            console.error('Audio file path issue - check if file exists at:', this.backgroundMusic.src);
        });

        this.backgroundMusic.addEventListener('canplaythrough', () => {
            console.log('Audio loaded successfully and ready to play');
        });

        // Start music with user interaction fallback
        const startMusic = () => {
            this.backgroundMusic.play().then(() => {
                this.musicStarted = true;
                this.startEyeGlowEffect();
                console.log('Red Light Green Light music started');
            }).catch(error => {
                console.log('Music autoplay blocked, will start on user interaction');
                // Try to start on first user interaction
                document.addEventListener('click', () => {
                    if (!this.musicStarted) {
                        this.backgroundMusic.play().then(() => {
                            this.musicStarted = true;
                            this.startEyeGlowEffect();
                        });
                    }
                }, { once: true });
            });
        };

        // Fade in the music smoothly
        this.backgroundMusic.volume = 0;
        startMusic();

        // Gradually increase volume
        let currentVolume = 0;
        const fadeInInterval = setInterval(() => {
            if (currentVolume < 0.3) {
                currentVolume += 0.02;
                this.backgroundMusic.volume = Math.min(currentVolume, 0.3);
            } else {
                clearInterval(fadeInInterval);
            }
        }, 100);
    }

    startEyeGlowEffect() {
        if (this.eyeGlowInterval) return;

        // Synchronize eye glow with music beats
        this.eyeGlowInterval = setInterval(() => {
            this.guardEyes.forEach((eye, index) => {
                // Stagger the glow effect slightly for each eye
                setTimeout(() => {
                    this.glowEye(eye);
                }, index * 100);
            });
        }, 1200); // Glow every 1.2 seconds to match typical beat
    }

    glowEye(eye) {
        if (!eye) return;

        // Add glow class
        eye.classList.add('guard-eye-glow');

        // Remove glow after short duration
        setTimeout(() => {
            eye.classList.remove('guard-eye-glow');
        }, 300);
    }

    stopBackgroundMusic() {
        if (this.backgroundMusic) {
            // Fade out smoothly
            let currentVolume = this.backgroundMusic.volume;
            const fadeOutInterval = setInterval(() => {
                if (currentVolume > 0) {
                    currentVolume -= 0.02;
                    this.backgroundMusic.volume = Math.max(currentVolume, 0);
                } else {
                    this.backgroundMusic.pause();
                    clearInterval(fadeOutInterval);
                }
            }, 50);
        }

        if (this.eyeGlowInterval) {
            clearInterval(this.eyeGlowInterval);
            this.eyeGlowInterval = null;
        }

        this.musicStarted = false;
    }

    setupVisibilityHandling() {
        // Pause/resume music when tab becomes hidden/visible
        document.addEventListener('visibilitychange', () => {
            if (this.backgroundMusic && this.musicStarted) {
                if (document.hidden) {
                    this.backgroundMusic.pause();
                    if (this.eyeGlowInterval) {
                        clearInterval(this.eyeGlowInterval);
                        this.eyeGlowInterval = null;
                    }
                } else {
                    this.backgroundMusic.play();
                    this.startEyeGlowEffect();
                }
            }
        });
    }

    // Manual test function for debugging
    testBackgroundMusic() {
        console.log('Testing background music...');
        const audio = document.getElementById('red-light-green-light');
        if (audio) {
            console.log('Audio element found');
            console.log('Audio src:', audio.src);
            console.log('Audio readyState:', audio.readyState);
            console.log('Audio networkState:', audio.networkState);

            audio.play().then(() => {
                console.log('Audio playing successfully');
                this.startEyeGlowEffect();
            }).catch(error => {
                console.error('Audio play failed:', error);
            });
        } else {
            console.error('Audio element not found');
        }
    }

    // Try different audio paths
    tryDifferentAudioPaths() {
        const audio = document.getElementById('red-light-green-light');
        if (!audio) return;

        const possiblePaths = [
            './Squid Game Red Light Green Light Sound Effect - tivin01\'s Sound Effects.mp3',
            'Squid Game Red Light Green Light Sound Effect - tivin01\'s Sound Effects.mp3',
            '/fahim/Squid Game Red Light Green Light Sound Effect - tivin01\'s Sound Effects.mp3',
            'assets/sounds/Squid Game Red Light Green Light Sound Effect - tivin01\'s Sound Effects.mp3'
        ];

        let pathIndex = 0;

        const tryNextPath = () => {
            if (pathIndex >= possiblePaths.length) {
                console.error('All audio paths failed. Please check if the file exists.');
                return;
            }

            const path = possiblePaths[pathIndex];
            console.log(`Trying audio path ${pathIndex + 1}/${possiblePaths.length}:`, path);

            audio.src = path;

            audio.addEventListener('canplaythrough', () => {
                console.log('Audio loaded successfully with path:', path);
                this.startBackgroundMusic();
            }, { once: true });

            audio.addEventListener('error', () => {
                console.log('Failed to load audio with path:', path);
                pathIndex++;
                tryNextPath();
            }, { once: true });

            audio.load();
        };

        tryNextPath();
    }
}

// Initialize the app when DOM is loaded
let squidApp;
document.addEventListener('DOMContentLoaded', () => {
    squidApp = new SquidLinkApp();
    window.squidApp = squidApp; // Make globally accessible for cache management

    // Add global test functions for debugging
    window.testBackgroundMusic = () => {
        if (squidApp) {
            squidApp.testBackgroundMusic();
        }
    };

    window.startMusic = () => {
        if (squidApp) {
            squidApp.startBackgroundMusic();
        }
    };

    window.tryDifferentPaths = () => {
        if (squidApp) {
            squidApp.tryDifferentAudioPaths();
        }
    };
});

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(registrationError => console.log('SW registration failed'));
    });
}