/* EXACT COPY FROM REACT VERSION - SquidLink CSS */

/* SQUID GAME FONT */
@font-face {
  font-family: 'Game Of Squids';
  src: url('../fonts/Game Of Squids.woff2') format('woff2'),
       url('../fonts/Game Of Squids.woff') format('woff'),
       url('../fonts/Game Of Squids.ttf') format('truetype'),
       url('../fonts/Game Of Squids.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}





:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 10%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 5%);
  --popover-foreground: hsl(0, 0%, 95%);
  --card: hsl(0, 0%, 5%);
  --card-foreground: hsl(0, 0%, 95%);
  --border: hsl(340, 100%, 35%);
  --input: hsl(0, 0%, 10%);
  --primary: hsl(340, 100%, 35%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(150, 100%, 35%);
  --secondary-foreground: hsl(0, 0%, 0%);
  --accent: hsl(150, 100%, 35%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 100%, 100%);
  --ring: hsl(340, 100%, 35%);
  --radius: 0.5rem;
  
  /* Squid Game Colors */
  --squid-pink: rgb(255, 0, 85);
  --squid-green: hsl(150, 100%, 35%);
  --squid-red: hsl(348, 83%, 47%);
  --electric-blue: hsl(195, 100%, 50%);
}

/* NFC Modal Animations */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeOutScale {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
}

/* NFC Button Enhanced Styles */
.nfc-button {
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(255, 0, 85, 0.3);
}

.nfc-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(255, 0, 85, 0.6);
}

.nfc-button:active {
  transform: scale(0.95);
}

/* NFC Pulse Animation */
.nfc-pulse {
  animation: nfcPulse 2s infinite;
}

@keyframes nfcPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(255, 0, 85, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0);
  }
}

/* Modal Contact Options Hover Effects */
#nfc-contact-modal a,
#nfc-contact-modal button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

#nfc-contact-modal a:hover,
#nfc-contact-modal button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

#nfc-contact-modal a::before,
#nfc-contact-modal button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

#nfc-contact-modal a:hover::before,
#nfc-contact-modal button:hover::before {
  left: 100%;
}

/* WhatsApp Button Special Styling */
.whatsapp-btn {
  background: linear-gradient(135deg, #25D366, #128C7E) !important;
  position: relative;
}

.whatsapp-btn:hover {
  background: linear-gradient(135deg, #128C7E, #25D366) !important;
  transform: translateY(-3px) scale(1.02);
}

/* VCF Download Button Special Styling */
.vcf-btn {
  background: linear-gradient(135deg, var(--squid-green), #00ff88) !important;
  color: black !important;
}

.vcf-btn:hover {
  background: linear-gradient(135deg, var(--squid-pink), var(--squid-red)) !important;
  color: white !important;
}

/* Mobile Responsiveness for NFC Modal */
@media (max-width: 768px) {
  #nfc-contact-modal .bg-gradient-to-br {
    margin: 1rem;
    padding: 1.5rem;
  }

  #nfc-contact-modal h3 {
    font-size: 1.5rem;
  }

  #nfc-contact-modal .space-y-4 > * {
    padding: 1rem;
    font-size: 0.9rem;
  }

  #nfc-contact-modal .text-2xl {
    font-size: 1.5rem;
  }
}

/* Enhanced NFC Button for Mobile */
@media (max-width: 768px) {
  .nfc-button {
    width: 4.5rem;
    height: 4.5rem;
  }

  .nfc-button .w-8 {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.2rem;
  }
}

/* Floating WhatsApp Button */
.floating-whatsapp {
  animation: floatWhatsApp 3s ease-in-out infinite;
  box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
}

.floating-whatsapp:hover {
  box-shadow: 0 8px 30px rgba(37, 211, 102, 0.6);
  animation-play-state: paused;
}

@keyframes floatWhatsApp {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* WhatsApp Button Pulse Effect */
.floating-whatsapp::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(37, 211, 102, 0.3);
  transform: translate(-50%, -50%) scale(1);
  animation: whatsappPulse 2s infinite;
  z-index: -1;
}

@keyframes whatsappPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Mobile adjustments for floating button */
@media (max-width: 768px) {
  .floating-whatsapp {
    bottom: 1rem;
    right: 1rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .floating-whatsapp svg {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* Notification Toast */
#notification-toast {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#notification-toast.show {
  transform: translateX(0) !important;
}

/* Mobile adjustments for notification */
@media (max-width: 768px) {
  #notification-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    transform: translateY(-100%);
  }

  #notification-toast.show {
    transform: translateY(0) !important;
  }
}

/* Guard Eye Glow Effect for Red Light Green Light */
.guard-eye,
.guard-eye-mobile,
.guard-eye-small {
  transition: all 0.3s ease;
  position: relative;
}

.guard-eye-glow {
  background: radial-gradient(circle, #ff0055 0%, #ff3377 50%, #ff0055 100%) !important;
  box-shadow:
    0 0 10px #ff0055,
    0 0 20px #ff0055,
    0 0 30px #ff0055,
    inset 0 0 10px rgba(255, 0, 85, 0.5) !important;
  animation: guardEyePulse 0.3s ease-in-out;
  transform: scale(1.2);
}

@keyframes guardEyePulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 5px #ff0055;
  }
  50% {
    transform: scale(1.3);
    box-shadow:
      0 0 15px #ff0055,
      0 0 25px #ff0055,
      0 0 35px #ff0055;
  }
  100% {
    transform: scale(1.2);
    box-shadow:
      0 0 10px #ff0055,
      0 0 20px #ff0055,
      0 0 30px #ff0055;
  }
}

/* Enhanced guard eye base styles */
.guard-eye,
.guard-eye-mobile,
.guard-eye-small {
  background: #333;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

/* Add subtle default glow */
.guard-eye::before,
.guard-eye-mobile::before,
.guard-eye-small::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(255, 0, 85, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-color: var(--border);
}

h2.text-4xl.md\:text-5xl.font-bold.text-white.mb-8 {
    text-align: center;
}

/* SMOOTH SCROLLING */
html {
  scroll-behavior: smooth;
}

html::-webkit-scrollbar {
  width: 8px;
}

.bg-gray-900\/50 {
    background-color: rgb(17 24 39 / 0%) !important;
} 
html::-webkit-scrollbar-track {
  background: #1a1a1a;
}

html::-webkit-scrollbar-thumb {
  background: rgb(255, 0, 85);
  border-radius: 4px;
}

html::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 0, 85, 0.8);
}
.top-4 {
    top: 8rem !important;
}
body {
  font-family: 'Poppins', sans-serif;
  background: var(--background);
  color: var(--foreground);
  cursor: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Game Of Squids', 'Bebas Neue', cursive;
}

/* SQUID GAME FONT FOR BOLD/HEADER ELEMENTS */
.font-bold,
.font-bebas,
.digital-display,
.loading-text,
.loading-destination,
.nfc-card,
button {
  font-family: 'Game Of Squids', 'Bebas Neue', cursive !important;
}





/* PARTICLES BACKGROUND INTEGRATION */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: transparent;
  pointer-events: none;
}

/* EXACT COMPONENT STYLES FROM REACT */
.squid-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--squid-pink);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* PREMIUM SMOOTH HOVER ANIMATIONS */
.card-3d {
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
}

.card-3d:hover {
  transform: perspective(1000px) rotateX(-5deg) rotateY(5deg) scale(1.05);
  box-shadow:
    0 20px 40px rgba(255, 0, 85, 0.3),
    0 10px 20px rgba(0, 0, 0, 0.4);
}

/* PROJECT CARD PLAYER TEXT POSITIONING */
.project-card .absolute.top-4.left-4,
.skill-card .absolute.top-4.left-4 {
  position: absolute !important;
  top: 1rem !important;
  left: 1rem !important;
  z-index: 10;
  line-height: 1.1;
  white-space: nowrap;
}

.project-card .absolute.top-4.right-4,
.skill-card .absolute.top-4.right-4 {
  position: absolute !important;
  top: 1rem !important;
  right: 1rem !important;
  z-index: 10;
}

/* SCROLL ANIMATION FOR NAVIGATION */
#main-nav {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(0);
}

/* Initial state - navigation is visible but normal */
#main-nav.initial {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(0);
}

/* Hidden state when scrolling down */
#main-nav.hidden {
  transform: translateY(-100%);
  opacity: 0;
}

/* Visible state when scrolling up or at top */
#main-nav.visible {
  transform: translateY(0);
  opacity: 1;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Scrolled state - smaller navigation */
#main-nav.scrolled {
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-container {
  padding: 0.5rem 0;
}

.nav-symbols {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-symbols {
  font-size: 1.25rem;
  transform: scale(0.8);
}

.nav-player-display {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-player-display {
  transform: translateX(-50%) scale(0.7);
  min-width: 70px !important;
}

#main-nav.scrolled .nav-player-display .digital-display {
  font-size: 1rem !important;
  padding: 0.3rem 0.5rem !important;
}

.nav-clock {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-clock {
  transform: scale(0.75);
}

.nav-toggle {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-toggle {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 0.8rem;
}

/* PREMIUM NAVIGATION HOVER EFFECTS */
.nav-symbols a {
  position: relative;
  display: inline-block;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.nav-symbols a::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 0, 85, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-symbols a:hover::before {
  width: 60px;
  height: 60px;
}

.nav-symbols a:hover {
  transform: scale(1.2) rotate(5deg);
  color: #ffffff;
  text-shadow:
    0 0 10px rgba(255, 0, 85, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(255, 0, 85, 0.4);
  filter: brightness(1.3);
}

.nav-symbols a:active {
  transform: scale(1.1) rotate(-2deg);
  transition: all 0.1s ease;
}

/* Premium glow effect for navigation symbols */
.nav-symbols a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 50%,
    transparent 100%);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-symbols a:hover::after {
  opacity: 1;
  transform: scale(1.3);
  animation: pulseGlow 1.5s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 5px rgba(255, 0, 85, 0.3);
  }
  to {
    box-shadow: 0 0 20px rgba(255, 0, 85, 0.6), 0 0 30px rgba(255, 0, 85, 0.3);
  }
}

/* PREMIUM DARK MODE TOGGLE HOVER */
.nav-toggle {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-toggle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-toggle:hover::before {
  width: 80px;
  height: 80px;
}

.nav-toggle:hover {
  transform: scale(1.15) rotate(10deg);
  background: linear-gradient(135deg, #00ff88 0%, #ff0055 100%);
  box-shadow:
    0 0 15px rgba(0, 255, 136, 0.5),
    0 0 25px rgba(255, 0, 85, 0.3),
    0 4px 15px rgba(0, 0, 0, 0.3);
  filter: brightness(1.2);
}

.nav-toggle:active {
  transform: scale(1.05) rotate(-5deg);
  transition: all 0.1s ease;
}

/* Premium ripple effect for toggle */
.nav-toggle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease-out;
  opacity: 0;
}

.nav-toggle:hover::after {
  width: 100px;
  height: 100px;
  opacity: 1;
  animation: rippleEffect 1s ease-out infinite;
}

@keyframes rippleEffect {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* PREMIUM DIGITAL PLAYER DISPLAY HOVER */
.digital-player-display {
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.digital-player-display:hover {
  transform: translateX(-50%) scale(1.05);
  filter: brightness(1.2) saturate(1.3);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 40px rgba(59, 130, 246, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.3);
}

.digital-player-display:hover .digital-display {
  color: #ffffff !important;
  text-shadow:
    0 0 10px rgba(255, 0, 85, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(255, 0, 85, 0.4);
  animation: digitalGlow 1.5s ease-in-out infinite alternate;
}

.digital-player-display:hover .bg-black {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  border-color: rgba(255, 0, 85, 0.5);
}

@keyframes digitalGlow {
  from {
    text-shadow:
      0 0 10px rgba(255, 0, 85, 0.8),
      0 0 20px rgba(255, 0, 85, 0.6);
  }
  to {
    text-shadow:
      0 0 15px rgba(255, 0, 85, 1),
      0 0 25px rgba(255, 0, 85, 0.8),
      0 0 35px rgba(255, 0, 85, 0.6);
  }
}

/* PREMIUM SLIDE ANIMATIONS */
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  70% {
    transform: translateY(5px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

/* Apply animations to navigation states */
#main-nav.visible {
  animation: slideInFromTop 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

#main-nav.hidden {
  animation: slideOutToTop 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Enhance the glow effect when navigation appears */
#main-nav.visible::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 50%,
    transparent 100%);
  animation: navGlow 2s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: -1;
}

@keyframes navGlow {
  from {
    opacity: 0.3;
    transform: scaleX(0.8);
  }
  to {
    opacity: 0.6;
    transform: scaleX(1.2);
  }
}

/* PROJECT CARD PREMIUM HOVER - DESKTOP ONLY */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  filter: brightness(1) saturate(1);
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Only apply hover effects on devices that can actually hover */
@media (hover: hover) and (pointer: fine) {
  .project-card:hover {
    transform: translateY(-8px) scale(1.02);
    filter: brightness(1.1) saturate(1.2);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.5),
      0 15px 30px rgba(255, 0, 85, 0.2);
  }
}

.project-card:active {
  transform: translateY(-4px) scale(1.01);
  transition: all 0.1s ease;
}

/* Prevent hover effects on touch devices */
@media (hover: none) {
  .project-card:hover {
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
  }
}

/* SQUID GAME NAVIGATION LOGO AND SHAPES */
.squid-logo-nav {
  animation: logoGlow 3s ease-in-out infinite alternate;
}

.squid-icon {
  animation: squidFloat 4s ease-in-out infinite;
}

.squid-body {
  box-shadow: 0 0 20px rgba(255, 0, 85, 0.5);
  animation: squidPulse 2s ease-in-out infinite;
}

.squid-tentacle {
  animation: tentacleWave 1.5s ease-in-out infinite;
  animation-delay: calc(var(--i, 0) * 0.2s);
}

.squid-tentacle:nth-child(3) { --i: 1; }
.squid-tentacle:nth-child(4) { --i: 2; }
.squid-tentacle:nth-child(5) { --i: 3; }
.squid-tentacle:nth-child(6) { --i: 4; }

.squid-text {
  text-shadow: 0 0 10px rgba(255, 0, 85, 0.8);
  animation: textGlow 2s ease-in-out infinite alternate;
}

/* Enhanced Navigation Shapes */
.nav-shape {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-shape:hover {
  background: rgba(255, 0, 85, 0.1);
  box-shadow: 0 0 20px rgba(255, 0, 85, 0.3);
}

.shape-symbol {
  font-size: 1.8rem;
  line-height: 1;
  text-shadow: 0 0 10px currentColor;
  transition: all 0.3s ease;
}

.shape-label {
  font-size: 0.6rem;
  font-weight: 600;
  margin-top: 0.2rem;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  font-family: 'Bebas Neue', sans-serif;
  letter-spacing: 1px;
}

.nav-shape:hover .shape-label {
  opacity: 1;
  transform: translateY(0);
}

.nav-shape:hover .shape-symbol {
  transform: scale(1.2);
  filter: drop-shadow(0 0 15px currentColor);
}

/* Individual shape colors */
.circle-shape { color: rgb(255, 0, 85); }
.triangle-shape { color: rgb(8, 106, 83); }
.square-shape { color: rgb(255, 170, 0); }

.circle-shape:hover { color: rgb(255, 50, 120); }
.triangle-shape:hover { color: rgb(20, 150, 100); }
.square-shape:hover { color: rgb(255, 200, 50); }

/* Navigation animations */
@keyframes logoGlow {
  0% { filter: brightness(1) saturate(1); }
  100% { filter: brightness(1.2) saturate(1.3); }
}

@keyframes squidFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-3px) rotate(1deg); }
  50% { transform: translateY(0px) rotate(0deg); }
  75% { transform: translateY(-2px) rotate(-1deg); }
}

@keyframes squidPulse {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 0, 85, 0.5); }
  50% { box-shadow: 0 0 30px rgba(255, 0, 85, 0.8); }
}

@keyframes tentacleWave {
  0%, 100% { transform: rotate(var(--rotation, 12deg)) scaleY(1); }
  50% { transform: rotate(calc(var(--rotation, 12deg) * 1.5)) scaleY(1.1); }
}

@keyframes textGlow {
  0% { text-shadow: 0 0 10px rgba(255, 0, 85, 0.8); }
  100% { text-shadow: 0 0 20px rgba(255, 0, 85, 1), 0 0 30px rgba(255, 0, 85, 0.5); }
}

/* Mobile navigation adjustments */
@media (max-width: 768px) {
  #main-nav {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(10, 10, 10, 0.98)) !important;
    backdrop-filter: blur(25px) saturate(1.2);
    border-bottom: 2px solid rgba(255, 0, 85, 0.3);
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.4),
      0 0 40px rgba(255, 0, 85, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  #main-nav:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.98), rgba(15, 15, 15, 1)) !important;
    border-bottom-color: rgba(255, 0, 85, 0.5);
    box-shadow:
      0 6px 30px rgba(0, 0, 0, 0.5),
      0 0 60px rgba(255, 0, 85, 0.2);
    transform: translateY(-1px);
  }

  .nav-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 0;
    position: relative;
    max-width: 100%;
    margin: 0 auto;
  }

  .nav-left {
    display: none; /* Hide squid logo completely on mobile */
  }

  .nav-symbols {
    display: flex;
    gap: 1.5rem;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    flex: 1;
    max-width: 100%;
  }

  .nav-shape {
    padding: 0.6rem;
    min-width: 52px;
    min-height: 52px;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .nav-shape::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 0, 85, 0.1), rgba(255, 0, 85, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
  }

  .nav-shape:hover::before,
  .nav-shape:active::before {
    opacity: 1;
  }

  .nav-shape:hover,
  .nav-shape:active {
    background: linear-gradient(135deg, rgba(255, 0, 85, 0.15), rgba(255, 0, 85, 0.08));
    border-color: rgba(255, 0, 85, 0.4);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 6px 20px rgba(255, 0, 85, 0.3),
      0 0 30px rgba(255, 0, 85, 0.1);
  }

  .shape-symbol {
    font-size: 1.4rem;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    margin-bottom: 0.1rem;
  }

  .nav-shape:hover .shape-symbol,
  .nav-shape:active .shape-symbol {
    color: #ff0055;
    transform: scale(1.1);
    text-shadow: 0 0 10px rgba(255, 0, 85, 0.5);
  }

  .shape-label {
    font-size: 0.4rem;
    opacity: 0.8;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 2;
  }

  .nav-shape:hover .shape-label {
    opacity: 1;
    color: #ff0055;
  }

  /* Position player display properly on mobile */
  .nav-player-display {
    position: absolute !important;
    right: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    left: auto !important;
    margin: 0 !important;
    min-width: 70px !important;
    max-width: 90px;
    z-index: 10;
  }
}

@media (max-width: 480px) {
  #main-nav {
    padding: 0.4rem 0.75rem;
  }

  .nav-container {
    gap: 0.75rem;
    justify-content: center;
  }

  .nav-left {
    display: none; /* Hide left section completely on small mobile */
  }

  .nav-symbols {
    gap: 0.75rem;
    justify-content: center;
    width: 100%;
  }

  .nav-shape {
    padding: 0.35rem;
    min-width: 44px;
    min-height: 44px;
    border-radius: 10px;
  }

  .shape-symbol {
    font-size: 1.2rem;
  }

  .shape-label {
    font-size: 0.35rem;
  }
}

/* Digital Player Display Mobile Optimization */
@media (max-width: 768px) {
  .nav-player-display {
    position: absolute !important;
    right: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    left: auto !important;
    margin: 0 !important;
    min-width: 70px !important;
    max-width: 90px;
    background: linear-gradient(135deg, #0ea5e9, #3b82f6) !important;
    border: 2px solid rgba(59, 130, 246, 0.3) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  }

  .nav-player-display .bg-black {
    padding: 0.4rem 0.6rem !important;
    background: rgba(0, 0, 0, 0.9) !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
  }

  .nav-player-display .player-number {
    font-size: 0.8rem !important;
    color: #60a5fa !important;
    font-weight: 700 !important;
  }
}

@media (max-width: 480px) {
  .nav-player-display {
    min-width: 60px !important;
    max-width: 80px;
    right: 0.75rem !important;
  }

  .nav-player-display .bg-black {
    padding: 0.3rem 0.5rem !important;
  }

  .nav-player-display .player-number {
    font-size: 0.7rem !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  #main-nav {
    padding: 0.3rem 0.5rem;
  }

  .nav-container {
    gap: 0.5rem;
  }

  .nav-symbols {
    gap: 0.5rem;
  }

  .nav-shape {
    min-width: 40px;
    min-height: 40px;
    padding: 0.3rem;
    border-radius: 8px;
  }

  .shape-symbol {
    font-size: 1rem;
  }

  .shape-label {
    font-size: 0.3rem;
  }

  .nav-player-display {
    min-width: 55px !important;
    max-width: 70px;
    right: 0.5rem !important;
  }

  .nav-player-display .player-number {
    font-size: 0.65rem !important;
  }
}

/* Mobile Touch Improvements */
@media (max-width: 768px) {
  /* Ensure minimum touch target size of 44px */
  .nav-shape {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  /* Better spacing for mobile */
  body {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Improve mobile navigation visibility */
  #main-nav {
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: blur(10px);
  }

  /* Better mobile intro positioning */
  .squid-intro {
    padding: 1rem;
    box-sizing: border-box;
  }
}

/* Landscape mobile optimization */
@media (max-width: 768px) and (orientation: landscape) {
  .intro-content {
    padding: 0.5rem;
  }

  .intro-shapes {
    margin-bottom: 1rem;
  }

  .intro-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .intro-line {
    font-size: 0.7rem;
  }

  .intro-subtitle {
    font-size: 0.6rem;
  }

  #main-nav {
    padding: 0.5rem 1rem;
  }

  .nav-container {
    gap: 0.5rem;
  }
}

/* Mobile Device Specific Styles */
.mobile-device #main-nav {
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 0, 85, 0.2);
}

.mobile-device .nav-container {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.mobile-device .nav-left {
  display: none; /* Hide squid logo on mobile */
}

.mobile-device .nav-symbols {
  justify-content: center;
  flex: 1;
}

.mobile-device .nav-shape {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.mobile-device .nav-shape:hover,
.mobile-device .nav-shape:active {
  background: rgba(255, 0, 85, 0.1);
  border-color: rgba(255, 0, 85, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 0, 85, 0.2);
}

.mobile-device .nav-player-display {
  position: absolute !important;
  right: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  left: auto !important;
  margin: 0 !important;
}

/* Prevent zoom on input focus (iOS) */
.mobile-device input,
.mobile-device select,
.mobile-device textarea {
  font-size: 16px !important;
}

/* Better touch targets for mobile */
.mobile-device .nav-shape {
  min-height: 48px;
  min-width: 48px;
  padding: 0.5rem;
}

.mobile-device .squid-logo-nav {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth scrolling for mobile */
.mobile-device {
  -webkit-overflow-scrolling: touch;
}

.mobile-device * {
  -webkit-tap-highlight-color: transparent;
}

/* Premium Mobile Navigation Effects */
@media (max-width: 768px) {
  .nav-shape {
    position: relative;
    overflow: hidden;
  }

  .nav-shape::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }

  .nav-shape:hover::before,
  .nav-shape:active::before {
    left: 100%;
  }

  /* Glowing effect for active states */
  .nav-shape:active {
    box-shadow:
      0 0 20px rgba(255, 0, 85, 0.4),
      0 4px 12px rgba(255, 0, 85, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Premium player display glow */
  .nav-player-display {
    animation: playerGlow 3s ease-in-out infinite alternate;
  }

  @keyframes playerGlow {
    0% {
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    100% {
      box-shadow:
        0 4px 20px rgba(59, 130, 246, 0.5),
        0 0 30px rgba(59, 130, 246, 0.2);
    }
  }
}

/* SQUID GAME INTRO ANIMATION */
.squid-intro {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #000000 0%, #1a0a0a 50%, #000000 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 25000;
  animation: introFadeIn 0.5s ease-out;
}

.intro-content {
  text-align: center;
  color: white;
  font-family: 'Bebas Neue', sans-serif;
}

.intro-shapes {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
  animation: shapesAppear 1s ease-out 0.5s both;
}

.intro-shape {
  font-size: 4rem;
  font-weight: bold;
  opacity: 0;
  transform: scale(0) rotate(180deg);
  animation: shapeReveal 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) both;
}

.circle-intro {
  color: rgb(255, 0, 85);
  animation-delay: 1s;
}

.triangle-intro {
  color: rgb(8, 106, 83);
  animation-delay: 1.3s;
}

.square-intro {
  color: rgb(255, 170, 0);
  animation-delay: 1.6s;
}

.intro-text {
  margin-bottom: 2rem;
  opacity: 0;
  animation: textSlideUp 1s ease-out 2s both;
}

.intro-line {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
}

.intro-title {
  font-size: 4rem;
  color: rgb(255, 0, 85);
  text-shadow: 0 0 30px rgba(255, 0, 85, 0.8);
  margin-bottom: 0.5rem;
  letter-spacing: 3px;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

.intro-subtitle {
  font-size: 1rem;
  color: rgb(8, 106, 83);
  letter-spacing: 1px;
  text-transform: uppercase;
}

.intro-loading {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  animation: loadingAppear 0.5s ease-out 3s both;
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: rgb(255, 0, 85);
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.6s;
}

/* Intro Animation Keyframes */
@keyframes introFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes shapesAppear {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shapeReveal {
  0% {
    opacity: 0;
    transform: scale(0) rotate(180deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes textSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  0% {
    text-shadow: 0 0 30px rgba(255, 0, 85, 0.8);
    transform: scale(1);
  }
  100% {
    text-shadow:
      0 0 40px rgba(255, 0, 85, 1),
      0 0 60px rgba(255, 0, 85, 0.6);
    transform: scale(1.02);
  }
}

@keyframes loadingAppear {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* Intro fade out animation */
.squid-intro.fade-out {
  animation: introFadeOut 1s ease-in-out forwards;
}

@keyframes introFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

/* Mobile intro adjustments */
@media (max-width: 768px) {
  .intro-content {
    padding: 1rem;
    max-width: 90vw;
  }

  .intro-shapes {
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .intro-shape {
    font-size: 2.5rem;
    min-width: 60px;
    text-align: center;
  }

  .intro-title {
    font-size: 2rem;
    line-height: 1.1;
    margin-bottom: 1rem;
  }

  .intro-line {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
  }

  .intro-subtitle {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .intro-loading {
    margin-top: 1.5rem;
  }
}

@media (max-width: 480px) {
  .squid-intro {
    padding: 0.5rem !important;
  }

  .intro-content {
    padding: 0.5rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }

  .intro-shapes {
    gap: 0.8rem;
    margin-bottom: 1rem;
    width: 100%;
    justify-content: center;
  }

  .intro-shape {
    font-size: 1.8rem;
    min-width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .intro-title {
    font-size: 1.6rem;
    word-break: break-word;
    line-height: 1;
    margin-bottom: 0.8rem;
  }

  .intro-line {
    font-size: 0.75rem;
    margin-bottom: 0.2rem;
  }

  .intro-subtitle {
    font-size: 0.65rem;
    padding: 0 0.5rem;
    line-height: 1.2;
  }

  .intro-loading {
    margin-top: 1rem;
  }
}

/* BUTTON PREMIUM HOVER EFFECTS */
.btn, button, .social-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  filter: brightness(1);
}

.btn:hover, button:hover {
  transform: translateY(-2px) scale(1.05);
  filter: brightness(1.2);
  box-shadow:
    0 10px 25px rgba(255, 0, 85, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:active, button:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* SOCIAL LINKS HOVER */
.social-link:hover {
  transform: translateY(-3px) scale(1.1);
  filter: brightness(1.3) saturate(1.5);
  box-shadow:
    0 8px 20px rgba(255, 0, 85, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
}

/* GUARD FIGURE HOVER */
.guard-figure:hover {
  transform: scale(1.05) rotate(2deg);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.guard-figure:hover .guard-eye-left,
.guard-figure:hover .guard-eye-right {
  animation: red-light-bloom 1s ease-in-out infinite;
  box-shadow:
    0 0 15px rgb(255, 0, 85),
    0 0 30px rgb(255, 0, 85),
    0 0 45px rgb(255, 0, 85);
}

/* ADVANCED SQUID PRELOADER SYSTEM */
.squid-preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: preloaderFadeIn 0.5s ease-in-out;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.squid-preloader.fade-out {
  opacity: 0;
  transform: scale(1.1);
  pointer-events: none;
}

.squid-preloader.hidden {
  display: none;
}

/* PREMIUM LOADING OVERLAY - EXACT NODE.JS MATCH */
.premium-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-in-out;
}

/* Preloader Content */
.preloader-content {
  text-align: center;
  color: white;
  animation: slideUp 0.8s ease-out;
  max-width: 500px;
  padding: 2rem;
}

.squid-logo-container {
  margin-bottom: 3rem;
}

.squid-logo {
  width: 150px;
  height: 150px;
  margin: 0 auto;
  position: relative;
  border: 4px solid rgb(255, 0, 85);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: logoSpin 4s linear infinite;
  background: radial-gradient(circle, rgba(255, 0, 85, 0.1) 0%, transparent 70%);
}

.logo-square {
  position: absolute;
  width: 25px;
  height: 25px;
  background: rgb(255, 0, 85);
  top: 15px;
  right: 15px;
  animation: logoElementPulse 2s ease-in-out infinite;
  animation-delay: 0.5s;
}

/* Loading Text */
.loading-text {
  margin-bottom: 3rem;
}

.glitch-text {
  animation: glitchText 3s ease-in-out infinite;
}

/* Progress Bar */
.progress-container {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 0, 85, 0.3);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgb(255, 0, 85), rgb(8, 106, 83));
  width: 0%;
  transition: width 0.3s ease;
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Loading Steps */
.loading-steps {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 2rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.step.active {
  color: rgb(255, 0, 85);
  transform: translateX(10px);
}

.step.completed {
  color: rgb(8, 106, 83);
}

.step-icon {
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background: rgb(255, 0, 85);
  color: white;
  animation: stepPulse 1s ease-in-out infinite;
}

.step.completed .step-icon {
  background: rgb(8, 106, 83);
  color: white;
}

.step.completed .step-icon::before {
  content: '✓';
}

/* Corner Circles */
.corner-circle {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 3px solid rgb(255, 0, 85);
  border-radius: 50%;
  animation: cornerPulse 2s ease-in-out infinite;
}

.corner-circle.top-left {
  top: 20px;
  left: 20px;
}

.corner-circle.top-right {
  top: 20px;
  right: 20px;
}

.corner-circle.bottom-left {
  bottom: 20px;
  left: 20px;
}

.corner-circle.bottom-right {
  bottom: 20px;
  right: 20px;
}

/* Haptic Feedback Indicator */
.haptic-indicator {
  position: absolute;
  bottom: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  opacity: 0;
  animation: hapticFadeIn 1s ease-in-out 2s forwards;
}

.haptic-pulse {
  width: 12px;
  height: 12px;
  background: rgb(8, 106, 83);
  border-radius: 50%;
  animation: hapticPulse 1.5s ease-in-out infinite;
}

.haptic-indicator.disabled {
  opacity: 0.3;
}

.haptic-indicator.disabled .haptic-pulse {
  background: rgba(255, 255, 255, 0.3);
  animation: none;
}

.loading-content {
  text-align: center;
  color: white;
  animation: slideUp 0.5s ease-out;
}

.loading-logo {
  margin: 0 auto 3rem;
}

.squid-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  position: relative;
  border: 3px solid rgb(255, 0, 85);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: logoSpin 3s linear infinite;
}

.logo-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgb(255, 0, 85);
  border-radius: 50%;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.logo-triangle {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 25px solid rgb(255, 0, 85);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.logo-square {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgb(255, 0, 85);
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.loading-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff88;
  margin-bottom: 1rem;
  letter-spacing: 3px;
}

.loading-destination {
  font-size: 2rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  margin-bottom: 2rem;
  text-shadow: 0 0 20px rgb(255, 0, 85);
  letter-spacing: 2px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.loading-dots span {
  width: 12px;
  height: 12px;
  background: #00ff88;
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes cornerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 20px rgba(255, 0, 85, 0);
  }
}

@keyframes logoSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* NFC ANIMATION OVERLAY */
.nfc-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 85, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: nfcFlash 4s ease-in-out;
}

.nfc-content {
  text-align: center;
  color: white;
  animation: nfcPulse 4s ease-in-out;
}

.nfc-card {
  font-size: 3rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  margin-bottom: 1rem;
  text-shadow: 0 0 20px rgb(255, 0, 85);
  animation: nfcGlow 1s ease-in-out infinite alternate;
}

.nfc-player {
  font-size: 2rem;
  color: #00ff88;
  margin-bottom: 1rem;
  text-shadow: 0 0 15px #00ff88;
}

.nfc-status {
  font-size: 1.5rem;
  color: white;
  opacity: 0.9;
}

@keyframes nfcFlash {
  0%, 100% { background: rgba(255, 0, 85, 0.1); }
  25%, 75% { background: rgba(255, 0, 85, 0.3); }
  50% { background: rgba(255, 0, 85, 0.5); }
}

@keyframes nfcPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes nfcGlow {
  from {
    text-shadow: 0 0 20px rgb(255, 0, 85);
    filter: brightness(1);
  }
  to {
    text-shadow: 0 0 40px rgb(255, 0, 85), 0 0 60px rgb(255, 0, 85);
    filter: brightness(1.3);
  }
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

.nfc-pulse {
  animation: pulse-glow 1s ease-in-out infinite;
}

.guard-figure {
  width: 8rem;
  height: 12rem;
  position: relative;
}

.guard-body {
  width: 100%;
  height: 100%;
  background: rgb(255, 0, 85);
  border-radius: 4rem 4rem 0rem 0rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.guard-mask-triangular {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  border: 2px solid white;
}

.guard-mask-round {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-round::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  border-radius: 50%;
}

.guard-eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 0.3rem;
  z-index: 10;
}

.guard-eye-left, .guard-eye-right {
  width: 0.4rem;
  height: 0.4rem;
  background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
  border-radius: 50%;
  animation: red-light-bloom 2s ease-in-out infinite;
  box-shadow:
    0 0 8px rgb(255, 0, 85),
    0 0 16px rgb(255, 0, 85),
    0 0 24px rgb(255, 0, 85);
}

.squid-shape {
  width: 4rem;
  height: 4rem;
  background: var(--squid-pink);
  border-radius: 50% 50% 0 50%;
  position: relative;
  transform: rotate(45deg);
}

.squid-shape::before {
  content: '';
  position: absolute;
  width: 1rem;
  height: 2rem;
  background: var(--squid-pink);
  border-radius: 50%;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
}

.squid-shape::after {
  content: '';
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
  top: 1rem;
  left: 1rem;
  transform: rotate(-45deg);
}

/* EXACT KEYFRAME ANIMATIONS FROM REACT */
@keyframes glitch {
  0% { transform: translate(0) }
  20% { transform: translate(-2px, 2px) }
  40% { transform: translate(-2px, -2px) }
  60% { transform: translate(2px, 2px) }
  80% { transform: translate(2px, -2px) }
  100% { transform: translate(0) }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px var(--squid-pink), 0 0 10px var(--squid-pink), 0 0 15px var(--squid-pink); }
  100% { box-shadow: 0 0 10px var(--squid-pink), 0 0 20px var(--squid-pink), 0 0 30px var(--squid-pink); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* PRELOADER ANIMATIONS */
@keyframes preloaderFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glitchText {
  0%, 90%, 100% { transform: translate(0); }
  10% { transform: translate(-2px, 1px); }
  20% { transform: translate(2px, -1px); }
  30% { transform: translate(-1px, 2px); }
  40% { transform: translate(1px, -2px); }
  50% { transform: translate(-2px, -1px); }
  60% { transform: translate(2px, 1px); }
  70% { transform: translate(-1px, -2px); }
  80% { transform: translate(1px, 2px); }
}

@keyframes logoElementPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 0, 85, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 0, 85, 0.8), 0 0 30px rgba(8, 106, 83, 0.4);
  }
}

@keyframes stepPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes hapticFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hapticPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

/* RESPONSIVE PRELOADER */
@media (max-width: 768px) {
  .squid-preloader {
    padding: 1rem;
  }

  .preloader-content {
    max-width: 90%;
    padding: 1rem;
  }

  .squid-logo {
    width: 120px;
    height: 120px;
  }

  .loading-text h2 {
    font-size: 2.5rem;
  }

  .loading-steps {
    font-size: 0.8rem;
  }

  .corner-circle {
    width: 40px;
    height: 40px;
    border-width: 2px;
  }

  .corner-circle.top-left,
  .corner-circle.top-right {
    top: 10px;
  }

  .corner-circle.bottom-left,
  .corner-circle.bottom-right {
    bottom: 10px;
  }

  .corner-circle.top-left,
  .corner-circle.bottom-left {
    left: 10px;
  }

  .corner-circle.top-right,
  .corner-circle.bottom-right {
    right: 10px;
  }

  .haptic-indicator {
    bottom: 15px;
    right: 15px;
    font-size: 0.7rem;
  }
}

/* CACHE MANAGEMENT UI ENHANCEMENTS */
.cache-management-modal {
  backdrop-filter: blur(10px);
  animation: modalFadeIn 0.3s ease-out;
}

.cache-modal-content {
  box-shadow: 0 20px 40px rgba(255, 0, 85, 0.3);
  animation: modalSlideUp 0.3s ease-out;
}

.cache-btn.clear { border-color: #ff4444; color: #ff4444; }
.cache-btn.clear:hover { background: #ff4444; }

.cache-btn.restore { border-color: #44ff44; color: #44ff44; }
.cache-btn.restore:hover { background: #44ff44; color: #000; }

.cache-btn.refresh { border-color: #ffaa00; color: #ffaa00; }
.cache-btn.refresh:hover { background: #ffaa00; color: #000; }

.cache-btn.close { border-color: #888; color: #888; }
.cache-btn.close:hover { background: #888; }

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* PERFORMANCE OPTIMIZATIONS */
.squid-preloader * {
  will-change: transform, opacity;
}

.squid-logo {
  contain: layout style paint;
}

.progress-fill {
  contain: layout style;
}

@keyframes liquid {
  0% { border-radius: 50%; transform: scale(1); }
  50% { border-radius: 60% 40% 30% 70%; transform: scale(1.1); }
  100% { border-radius: 50%; transform: scale(1); }
}

@keyframes guard-sway {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

@keyframes number-flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite alternate;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

/* ENHANCED GLITCH TEXT WITH NAME CYCLING */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(0, 255, 136, 0.4);
  transition: all 0.3s ease;
}

.glitch-text #name-text {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glitch-text::before {
  color: rgb(255, 0, 85);
  z-index: -1;
  text-shadow: 2px 0 0 rgb(255, 0, 85);
}

.glitch-text::after {
  color: #00ff88;
  z-index: -2;
  text-shadow: -2px 0 0 #00ff88;
}

/* Active glitch state for name cycling */
.glitch-text.glitch-active {
  animation: glitch-intense 0.8s infinite;
  text-shadow:
    0 0 15px rgba(255, 255, 255, 1),
    0 0 25px rgba(255, 0, 85, 0.8),
    0 0 35px rgba(0, 255, 136, 0.6);
}

.glitch-text.glitch-active::before {
  opacity: 0.8;
  animation: glitch-1-intense 0.2s infinite;
}

.glitch-text.glitch-active::after {
  opacity: 0.6;
  animation: glitch-2-intense 0.25s infinite;
}

/* EXACT GLITCH KEYFRAMES FROM REACT */
@keyframes glitch-main {
  0%, 90%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-5px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -5px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(5px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-3px, 3px) scale(1.01);
    filter: hue-rotate(45deg);
  }
  60% {
    transform: translate(3px, -3px) scale(0.99);
    filter: hue-rotate(135deg);
  }
  70% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(225deg);
  }
  80% {
    transform: translate(4px, 1px);
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

/* INTENSE GLITCH ANIMATIONS FOR NAME CYCLING */
@keyframes glitch-intense {
  0%, 90%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg) brightness(1);
  }
  5% {
    transform: translate(-8px, 4px) skew(2deg);
    filter: hue-rotate(45deg) brightness(1.2);
  }
  10% {
    transform: translate(-4px, -8px) skew(-1deg);
    filter: hue-rotate(90deg) brightness(0.8);
  }
  15% {
    transform: translate(6px, 2px) skew(1deg);
    filter: hue-rotate(135deg) brightness(1.1);
  }
  20% {
    transform: translate(-2px, -6px) skew(-2deg);
    filter: hue-rotate(180deg) brightness(0.9);
  }
  25% {
    transform: translate(4px, 8px) skew(1.5deg);
    filter: hue-rotate(225deg) brightness(1.3);
  }
  30% {
    transform: translate(-6px, -2px) skew(-1.5deg);
    filter: hue-rotate(270deg) brightness(0.7);
  }
  35% {
    transform: translate(8px, -4px) skew(2.5deg);
    filter: hue-rotate(315deg) brightness(1.4);
  }
  40% {
    transform: translate(-3px, 6px) skew(-0.5deg);
    filter: hue-rotate(360deg) brightness(1);
  }
}

@keyframes glitch-1-intense {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  10% {
    transform: translate(-8px, 6px);
    clip-path: polygon(0 20%, 100% 20%, 100% 35%, 0 35%);
    opacity: 1;
  }
  20% {
    transform: translate(6px, -8px);
    clip-path: polygon(0 40%, 100% 40%, 100% 55%, 0 55%);
    opacity: 0.8;
  }
  30% {
    transform: translate(-4px, 4px);
    clip-path: polygon(0 60%, 100% 60%, 100% 75%, 0 75%);
    opacity: 0.9;
  }
  40% {
    transform: translate(8px, -2px);
    clip-path: polygon(0 80%, 100% 80%, 100% 95%, 0 95%);
    opacity: 0.7;
  }
}

@keyframes glitch-2-intense {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 20%, 0 20%);
    opacity: 0.8;
  }
  15% {
    transform: translate(6px, -8px);
    clip-path: polygon(0 25%, 100% 25%, 100% 40%, 0 40%);
    opacity: 0.9;
  }
  30% {
    transform: translate(-8px, 4px);
    clip-path: polygon(0 45%, 100% 45%, 100% 60%, 0 60%);
    opacity: 0.7;
  }
  45% {
    transform: translate(4px, 6px);
    clip-path: polygon(0 65%, 100% 65%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  60% {
    transform: translate(-6px, -4px);
    clip-path: polygon(0 85%, 100% 85%, 100% 100%, 0 100%);
    opacity: 0.6;
  }
}

@keyframes red-light-bloom {
  0%, 100% {
    box-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 16px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85);
    opacity: 0.8;
  }
  50% {
    box-shadow:
      0 0 12px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85),
      0 0 36px rgb(255, 0, 85);
    opacity: 1;
  }
}

/* ESSENTIAL UTILITY CLASSES */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }
.pointer-events-none { pointer-events: none; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.text-white { color: white; }
.text-squid-pink { color: var(--squid-pink); }
.text-squid-green { color: var(--squid-green); }
.text-squid-red { color: var(--squid-red); }
.bg-black { background-color: black; }
.bg-white { background-color: white; }
.bg-squid-pink { background-color: var(--squid-pink); }
.bg-squid-green { background-color: var(--squid-green); }
.bg-card { background-color: var(--card); }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-squid-pink { border-color: var(--squid-pink); }
.border-squid-green { border-color: var(--squid-green); }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-liquid { animation: liquid 4s ease-in-out infinite; }
.animate-guard-sway { animation: guard-sway 3s ease-in-out infinite; }
.animate-number-flip { animation: number-flip 2s ease-in-out infinite; }
.animate-shake { animation: shake 0.5s ease-in-out infinite; }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
  h3.text-2xl.font-bold.text-squid-pink.mb-4
 {
    text-align: center;
    font-size: 18px;
}
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:text-5xl { font-size: 3rem; }
  .lg\\:text-6xl { font-size: 3.75rem; }
}

/* ABOUT ME SECTION */
.profile-card {
  position: relative;
  animation: profileFloat 6s ease-in-out infinite;
  box-shadow:
    0 0 30px rgba(255, 0, 85, 0.3),
    inset 0 0 30px rgba(255, 0, 85, 0.1);
}

.profile-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: transparent;
    border-radius: 18px;
    z-index: -1;
    animation: borderGlow 3s linear infinite;
}

.about-content p {
  
  font-size: 1rem;
  line-height: 1.7;
}
button.w-full.mt-6.bg-squid-green.text-black.font-bold.py-3.rounded-lg.hover\:bg-squid-pink.hover\:text-white.transition-all.duration-300 .bg-squid-green{
    background-color: white !important;
}



.about-content span.text-squid-pink,
.about-content span.text-squid-green {
  text-shadow: 0 0 10px currentColor;
}

@keyframes profileFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes borderGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ADVANCED DIGITAL CLOCK */
.digital-clock-container {
  position: relative;
}

.digital-clock-frame {
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border: 2px solid #333;
  border-radius: 8px;
  padding: 4px;
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.5);
}

.digital-clock-screen {
  background: #000;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 8px 12px;
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.digital-clock-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 1px;
  opacity: 0.1;
}

.clock-pixel {
  background: rgb(255, 0, 85);
  border-radius: 1px;
  animation: pixelFlicker 3s ease-in-out infinite;
}

.clock-pixel:nth-child(odd) {
  animation-delay: 0.5s;
}

.clock-pixel:nth-child(3n) {
  animation-delay: 1s;
}

.digital-clock-display {
  position: relative;
  z-index: 2;
  text-align: center;
}

#digital-time {
  font-family: 'Game Of Squids', 'Courier New', monospace;
  font-size: 1.2rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  text-shadow:
    0 0 5px rgb(255, 0, 85),
    0 0 10px rgb(255, 0, 85),
    0 0 15px rgb(255, 0, 85);
  letter-spacing: 2px;
  animation: digitalGlow 2s ease-in-out infinite alternate;
}

.clock-date {
  margin-top: 2px;
}

#digital-date {
  font-family: 'Game Of Squids', 'Courier New', monospace;
  font-size: 0.7rem;
  color: #00ff88;
  text-shadow: 0 0 3px #00ff88;
  letter-spacing: 1px;
  opacity: 0.9;
}

@keyframes pixelFlicker {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes digitalGlow {
  from {
    text-shadow:
      0 0 5px rgb(255, 0, 85),
      0 0 10px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85);
  }
  to {
    text-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85),
      0 0 25px rgb(255, 0, 85);
  }
}

/* MOBILE RESPONSIVE IMPROVEMENTS */
@media (max-width: 768px) {
  /* Mobile-specific fixes */
  body {
    cursor: auto; /* Disable custom cursor on mobile */
  }

  .squid-cursor {
    display: none; /* Hide custom cursor on mobile */
  }

  /* Fix NFC button touch issues */
  .nfc-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    position: relative;
    z-index: 10;
  }

  .nfc-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Mobile navigation improvements */
  nav {
    padding: 0.75rem 1rem;
  }

  nav .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Mobile player number display - make it smaller and responsive */
  .digital-player-display {
    min-width: 70px !important;
    transform: translateX(-50%) scale(0.75) !important;
  }

  .digital-display {
    font-size: 1rem !important;
    padding: 0.4rem 0.6rem !important;
    letter-spacing: 1px !important;
  }

  /* Fix project card player text positioning on mobile */
  .project-card .absolute.top-4.left-4 {
    top: 0.75rem !important;
    left: 0.75rem !important;
    font-size: 1rem !important;
    line-height: 1.2 !important;
  }

  .skill-card .absolute.top-4.left-4 {
    top: 0.75rem !important;
    left: 0.75rem !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  /* Mobile hero section */
  .hero-section h1,
  .glitch-text {
    font-size: 2.5rem !important;
    line-height: 1.1;
  }

  .hero-section .bg-squid-green {
    font-size: 0.9rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  /* Mobile project cards */
  .project-card,
  .skill-card {
    height: 220px !important;
  }

  .project-card .text-2xl,
  .skill-card .text-2xl {
    font-size: 1.1rem !important;
  }

  .project-card .text-sm,
  .skill-card .text-sm {
    font-size: 0.8rem !important;
  }

  /* Mobile guards */
  .guard-figure-mobile {
    width: 3rem !important;
    height: 4.5rem !important;
  }

  .guard-body-mobile {
    width: 100%;
    height: 100%;
    background: rgb(255, 0, 85);
    border-radius: 1.5rem 1.5rem 0rem 0rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .guard-mask-triangular-mobile,
  .guard-mask-round-mobile {
    position: absolute;
    top: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 1.5rem;
    background: black;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .guard-mask-triangular-mobile::before {
    content: '';
    width: 0.6rem;
    height: 0.6rem;
    background: white;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border: 1px solid white;
  }

  .guard-mask-round-mobile::before {
    content: '';
    width: 0.6rem;
    height: 0.6rem;
    background: white;
    border-radius: 50%;
  }

  .guard-eye-mobile {
    width: 0.15rem;
    height: 0.15rem;
    background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
    border-radius: 50%;
    animation: red-light-bloom 2s ease-in-out infinite;
    box-shadow:
      0 0 3px rgb(255, 0, 85),
      0 0 6px rgb(255, 0, 85),
      0 0 9px rgb(255, 0, 85);
  }

  /* Mobile contact form */
  .contact-form {
    padding: 1rem !important;
  }

  /* Mobile about section */
  .profile-card {
    margin-bottom: 2rem;
  }

  .about-content p {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  /* Mobile text sizes */
  .text-4xl { font-size: 1.8rem !important; }
  .text-5xl { font-size: 2.2rem !important; }
  .text-6xl { font-size: 2.8rem !important; }
  .text-8xl { font-size: 3.5rem !important; }
  .text-9xl { font-size: 4rem !important; }

  /* Mobile-specific improvements */
  .mobile-device .card-3d:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  .mobile-device .project-card:hover,
  .mobile-device .skill-card:hover {
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
  }

  /* Better mobile scrolling */
  body.mobile-device {
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
  }

  /* Mobile navigation symbols */
  nav .text-squid-pink a {
    padding: 0.5rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
  }

  /* Mobile digital clock adjustments */
  .digital-clock-container {
    transform: scale(0.8);
    transform-origin: center;
  }

  .digital-clock-screen {
    min-width: 100px !important;
    padding: 4px 8px !important;
  }

  #digital-time {
    font-size: 0.9rem !important;
    letter-spacing: 1px !important;
  }

  #digital-date {
    font-size: 0.5rem !important;
  }

  /* Mobile social links */
  .social-link {
    min-width: 48px !important;
    min-height: 48px !important;
  }

  /* Mobile form improvements */
  input, textarea, button {
    font-size: 16px !important; /* Prevent zoom on iOS */
  }

  /* Mobile loading overlay */
  .premium-loading-overlay .loading-text {
    font-size: 1.2rem !important;
  }

  .premium-loading-overlay .loading-destination {
    font-size: 1.5rem !important;
  }
  h3.text-2xl.font-bold.text-squid-pink.mb-4
 {
    text-align: center;
    font-size: 18px;
}
}

/* Additional mobile improvements for very small screens */
@media (max-width: 480px) {
  .hero-section h1,
  .glitch-text {
    font-size: 2rem !important;
  }

  .hero-section .bg-squid-green {
    font-size: 0.8rem !important;
    padding: 0.3rem 0.6rem !important;
  }

  .digital-player-display {
    transform: translateX(-50%) scale(0.65) !important;
    min-width: 60px !important;
  }

  .digital-display {
    font-size: 0.8rem !important;
    padding: 0.3rem 0.5rem !important;
  }

  /* Further reduce project card player text on very small screens */
  .project-card .absolute.top-4.left-4 {
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .skill-card .absolute.top-4.left-4 {
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.65rem !important;
  }

  .project-card,
  .skill-card {
    height: 200px !important;
  }

  .guard-figure-mobile {
    width: 2.5rem !important;
    height: 3.5rem !important;
  }

  nav {
    padding: 0.5rem !important;
  }

  .text-4xl { font-size: 1.5rem !important; }
  .text-5xl { font-size: 1.8rem !important; }
  .text-6xl { font-size: 2.2rem !important; }

  /* Very small screen digital clock */
  .digital-clock-container {
    transform: scale(0.65) !important;
  }

  .digital-clock-screen {
    min-width: 80px !important;
    padding: 2px 4px !important;
  }

  #digital-time {
    font-size: 0.7rem !important;
  }

  #digital-date {
    font-size: 0.4rem !important;
  }
}

/* SCROLL REVEAL ANIMATIONS */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Different reveal animations */
.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-100px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-scale {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

.scroll-reveal-fade {
  opacity: 0;
  transition: opacity 1s ease-out;
}

.scroll-reveal-fade.revealed {
  opacity: 1;
}

/* Staggered animations for multiple elements */
.scroll-reveal-stagger {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-stagger.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Add delays for staggered effect */
.scroll-reveal-stagger:nth-child(1) { transition-delay: 0.1s; }
.scroll-reveal-stagger:nth-child(2) { transition-delay: 0.2s; }
.scroll-reveal-stagger:nth-child(3) { transition-delay: 0.3s; }
.scroll-reveal-stagger:nth-child(4) { transition-delay: 0.4s; }
.scroll-reveal-stagger:nth-child(5) { transition-delay: 0.5s; }
.scroll-reveal-stagger:nth-child(6) { transition-delay: 0.6s; }

/* Squid Game themed reveal effects */
.squid-reveal {
  opacity: 0;
  transform: translateY(50px) rotateX(15deg);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: blur(5px);
}

.squid-reveal.revealed {
  opacity: 1;
  transform: translateY(0) rotateX(0deg);
  filter: blur(0px);
}

/* Glitch reveal effect */
.glitch-reveal {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out;
  position: relative;
}

.glitch-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.glitch-reveal.revealed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 50%,
    transparent 100%);
  animation: glitchSweep 0.8s ease-out;
  pointer-events: none;
}

@keyframes glitchSweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}
